﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{CA219BE4-62FE-3836-9AC6-4B4E98F41156}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\OpenCV\opencv\build\include;E:\eigen-3.4.0;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\OpenCV\opencv\build\include;E:\eigen-3.4.0;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\OpenCV\opencv\build\include;E:\eigen-3.4.0;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\OpenCV\opencv\build\include;E:\eigen-3.4.0;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\95f2aa3ea2082437301068457ec2133c\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
E:\Cmake\bin\cmake.exe -SE:/PETlab/图形/hw/HHMM/Hw2 -BE:/PETlab/图形/hw/HHMM/Hw2/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/PETlab/图形/hw/HHMM/Hw2/build/Rasterizer.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\Cmake\share\cmake-3.30\Modules\CMakeCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageMessage.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;E:\OpenCV\opencv\build\OpenCVConfig-version.cmake;E:\OpenCV\opencv\build\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules.cmake;E:\PETlab\图形\hw\HHMM\Hw2\CMakeLists.txt;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCXXCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeRCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
E:\Cmake\bin\cmake.exe -SE:/PETlab/图形/hw/HHMM/Hw2 -BE:/PETlab/图形/hw/HHMM/Hw2/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/PETlab/图形/hw/HHMM/Hw2/build/Rasterizer.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\Cmake\share\cmake-3.30\Modules\CMakeCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageMessage.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;E:\OpenCV\opencv\build\OpenCVConfig-version.cmake;E:\OpenCV\opencv\build\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules.cmake;E:\PETlab\图形\hw\HHMM\Hw2\CMakeLists.txt;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCXXCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeRCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
E:\Cmake\bin\cmake.exe -SE:/PETlab/图形/hw/HHMM/Hw2 -BE:/PETlab/图形/hw/HHMM/Hw2/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/PETlab/图形/hw/HHMM/Hw2/build/Rasterizer.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\Cmake\share\cmake-3.30\Modules\CMakeCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageMessage.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;E:\OpenCV\opencv\build\OpenCVConfig-version.cmake;E:\OpenCV\opencv\build\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules.cmake;E:\PETlab\图形\hw\HHMM\Hw2\CMakeLists.txt;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCXXCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeRCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
E:\Cmake\bin\cmake.exe -SE:/PETlab/图形/hw/HHMM/Hw2 -BE:/PETlab/图形/hw/HHMM/Hw2/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/PETlab/图形/hw/HHMM/Hw2/build/Rasterizer.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\Cmake\share\cmake-3.30\Modules\CMakeCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageMessage.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;E:\OpenCV\opencv\build\OpenCVConfig-version.cmake;E:\OpenCV\opencv\build\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules.cmake;E:\PETlab\图形\hw\HHMM\Hw2\CMakeLists.txt;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCXXCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeRCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>