
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      适用于 .NET Framework MSBuild 版本 17.14.14+a129329f1
      生成启动时间为 2025/7/28 15:19:44。
      
      节点 1 上的项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\3.30.0-rc1\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
        正在创建目录“Debug\\CompilerIdC.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        正在对“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”执行 Touch 任务。
      ClCompile:
        E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\3.30.0-rc1\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate”执行 Touch 任务。
      已完成生成项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\3.30.0-rc1\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:02.39
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        E:/PETlab/图形/hw/HHMM/Hw2/build/CMakeFiles/3.30.0-rc1/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      适用于 .NET Framework MSBuild 版本 17.14.14+a129329f1
      生成启动时间为 2025/7/28 15:19:48。
      
      节点 1 上的项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\3.30.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
        正在创建目录“Debug\\CompilerIdCXX.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        正在对“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”执行 Touch 任务。
      ClCompile:
        E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\3.30.0-rc1\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate”执行 Touch 任务。
      已完成生成项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\3.30.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:02.29
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        E:/PETlab/图形/hw/HHMM/Hw2/build/CMakeFiles/3.30.0-rc1/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "E:/PETlab/\u56fe\u5f62/hw/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-8yucrk"
      binary: "E:/PETlab/\u56fe\u5f62/hw/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-8yucrk"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/PETlab/图形/hw/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-8yucrk'
        
        Run Build Command(s): E:/VS/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_32e64.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.14+a129329f1
        生成启动时间为 2025/7/28 15:19:51。
        
        节点 1 上的项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8yucrk\\cmTC_32e64.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_32e64.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8yucrk\\Debug\\”。
          正在创建目录“cmTC_32e64.dir\\Debug\\cmTC_32e64.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_32e64.dir\\Debug\\cmTC_32e64.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_32e64.dir\\Debug\\cmTC_32e64.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_32e64.dir\\Debug\\\\" /Fd"cmTC_32e64.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "E:\\Cmake\\share\\cmake-3.30\\Modules\\CMakeCCompilerABI.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35213 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_32e64.dir\\Debug\\\\" /Fd"cmTC_32e64.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "E:\\Cmake\\share\\cmake-3.30\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8yucrk\\Debug\\cmTC_32e64.exe" /INCREMENTAL /ILK:"cmTC_32e64.dir\\Debug\\cmTC_32e64.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/PETlab/图形/hw/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-8yucrk/Debug/cmTC_32e64.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/PETlab/图形/hw/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-8yucrk/Debug/cmTC_32e64.lib" /MACHINE:X64  /machine:x64 cmTC_32e64.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_32e64.vcxproj -> E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8yucrk\\Debug\\cmTC_32e64.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_32e64.dir\\Debug\\cmTC_32e64.tlog\\unsuccessfulbuild”。
          正在对“cmTC_32e64.dir\\Debug\\cmTC_32e64.tlog\\cmTC_32e64.lastbuildstate”执行 Touch 任务。
        已完成生成项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8yucrk\\cmTC_32e64.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:02.24
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': E:/VS/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "E:/VS/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35213.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "E:/PETlab/\u56fe\u5f62/hw/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-paisha"
      binary: "E:/PETlab/\u56fe\u5f62/hw/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-paisha"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/PETlab/图形/hw/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-paisha'
        
        Run Build Command(s): E:/VS/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_fa492.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.14+a129329f1
        生成启动时间为 2025/7/28 15:19:54。
        
        节点 1 上的项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\CMakeScratch\\TryCompile-paisha\\cmTC_fa492.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_fa492.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\CMakeScratch\\TryCompile-paisha\\Debug\\”。
          正在创建目录“cmTC_fa492.dir\\Debug\\cmTC_fa492.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_fa492.dir\\Debug\\cmTC_fa492.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_fa492.dir\\Debug\\cmTC_fa492.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_fa492.dir\\Debug\\\\" /Fd"cmTC_fa492.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "E:\\Cmake\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35213 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_fa492.dir\\Debug\\\\" /Fd"cmTC_fa492.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "E:\\Cmake\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\CMakeScratch\\TryCompile-paisha\\Debug\\cmTC_fa492.exe" /INCREMENTAL /ILK:"cmTC_fa492.dir\\Debug\\cmTC_fa492.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/PETlab/图形/hw/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-paisha/Debug/cmTC_fa492.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/PETlab/图形/hw/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-paisha/Debug/cmTC_fa492.lib" /MACHINE:X64  /machine:x64 cmTC_fa492.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_fa492.vcxproj -> E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\CMakeScratch\\TryCompile-paisha\\Debug\\cmTC_fa492.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_fa492.dir\\Debug\\cmTC_fa492.tlog\\unsuccessfulbuild”。
          正在对“cmTC_fa492.dir\\Debug\\cmTC_fa492.tlog\\cmTC_fa492.lastbuildstate”执行 Touch 任务。
        已完成生成项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw2\\build\\CMakeFiles\\CMakeScratch\\TryCompile-paisha\\cmTC_fa492.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:02.23
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': E:/VS/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "E:/VS/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35213.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeMinGWFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mingw32-make.exe"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
      - "/REGISTRY-NOTFOUND/bin/"
      - "c:/MinGW/bin/"
      - "/MinGW/bin/"
      - "/REGISTRY-NOTFOUND/MinGW/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/mingw32-make.exe.com"
    found: "E:/msys64/ucrt64/bin/mingw32-make.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: E:/msys64/ucrt64/bin/gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        D:/pet/CODE/图形/HHMM/Hw2/build/CMakeFiles/4.1.0/CompilerIdC/a.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ar"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/ar.com"
    found: "E:/msys64/ucrt64/bin/ar.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ranlib"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/ranlib.com"
    found: "E:/msys64/ucrt64/bin/ranlib.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "strip"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/strip.com"
    found: "E:/msys64/ucrt64/bin/strip.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/ld.com"
    found: "E:/msys64/ucrt64/bin/ld.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nm"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/nm.com"
    found: "E:/msys64/ucrt64/bin/nm.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objdump"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/objdump.com"
    found: "E:/msys64/ucrt64/bin/objdump.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objcopy"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/objcopy.com"
    found: "E:/msys64/ucrt64/bin/objcopy.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "readelf"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/readelf.com"
    found: "E:/msys64/ucrt64/bin/readelf.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dlltool"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/dlltool.com"
    found: "E:/msys64/ucrt64/bin/dlltool.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "addr2line"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/addr2line.com"
    found: "E:/msys64/ucrt64/bin/addr2line.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/tapi.com"
      - "E:/msys64/ucrt64/bin/tapi.exe"
      - "E:/msys64/ucrt64/bin/tapi"
      - "E:/msys64/usr/bin/tapi.com"
      - "E:/msys64/usr/bin/tapi.exe"
      - "E:/msys64/usr/bin/tapi"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/tapi.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/tapi.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/tapi"
      - "C:/Windows/System32/tapi.com"
      - "C:/Windows/System32/tapi.exe"
      - "C:/Windows/System32/tapi"
      - "C:/Windows/tapi.com"
      - "C:/Windows/tapi.exe"
      - "C:/Windows/tapi"
      - "C:/Windows/System32/wbem/tapi.com"
      - "C:/Windows/System32/wbem/tapi.exe"
      - "C:/Windows/System32/wbem/tapi"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi"
      - "C:/Windows/System32/OpenSSH/tapi.com"
      - "C:/Windows/System32/OpenSSH/tapi.exe"
      - "C:/Windows/System32/OpenSSH/tapi"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/tapi.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/tapi.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/tapi"
      - "D:/Git/cmd/tapi.com"
      - "D:/Git/cmd/tapi.exe"
      - "D:/Git/cmd/tapi"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/tapi.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/tapi.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/tapi"
      - "C:/Program Files/dotnet/tapi.com"
      - "C:/Program Files/dotnet/tapi.exe"
      - "C:/Program Files/dotnet/tapi"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/tapi.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/tapi.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/tapi"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/tapi.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/tapi.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/tapi"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/tapi.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/tapi.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/tapi"
      - "E:/Node/tapi.com"
      - "E:/Node/tapi.exe"
      - "E:/Node/tapi"
      - "C:/Program Files/CMake/bin/tapi.com"
      - "C:/Program Files/CMake/bin/tapi.exe"
      - "C:/Program Files/CMake/bin/tapi"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi"
      - "E:/Node/node_globle/tapi.com"
      - "E:/Node/node_globle/tapi.exe"
      - "E:/Node/node_globle/tapi"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi"
      - "E:/opencv/build/x64/vc15/bin/tapi.com"
      - "E:/opencv/build/x64/vc15/bin/tapi.exe"
      - "E:/opencv/build/x64/vc15/bin/tapi"
      - "E:/cursor/resources/app/bin/tapi.com"
      - "E:/cursor/resources/app/bin/tapi.exe"
      - "E:/cursor/resources/app/bin/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-14.2"
      - "gcc-ar-14"
      - "gcc-ar14"
      - "gcc-ar"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/gcc-ar-14.2.com"
      - "E:/msys64/ucrt64/bin/gcc-ar-14.2.exe"
      - "E:/msys64/ucrt64/bin/gcc-ar-14.2"
      - "E:/msys64/usr/bin/gcc-ar-14.2.com"
      - "E:/msys64/usr/bin/gcc-ar-14.2.exe"
      - "E:/msys64/usr/bin/gcc-ar-14.2"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-14.2.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-14.2.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-14.2"
      - "C:/Windows/System32/gcc-ar-14.2.com"
      - "C:/Windows/System32/gcc-ar-14.2.exe"
      - "C:/Windows/System32/gcc-ar-14.2"
      - "C:/Windows/gcc-ar-14.2.com"
      - "C:/Windows/gcc-ar-14.2.exe"
      - "C:/Windows/gcc-ar-14.2"
      - "C:/Windows/System32/wbem/gcc-ar-14.2.com"
      - "C:/Windows/System32/wbem/gcc-ar-14.2.exe"
      - "C:/Windows/System32/wbem/gcc-ar-14.2"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-14.2.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-14.2.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-14.2"
      - "C:/Windows/System32/OpenSSH/gcc-ar-14.2.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-14.2.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-14.2"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-14.2.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-14.2.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-14.2"
      - "D:/Git/cmd/gcc-ar-14.2.com"
      - "D:/Git/cmd/gcc-ar-14.2.exe"
      - "D:/Git/cmd/gcc-ar-14.2"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-14.2.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-14.2.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-14.2"
      - "C:/Program Files/dotnet/gcc-ar-14.2.com"
      - "C:/Program Files/dotnet/gcc-ar-14.2.exe"
      - "C:/Program Files/dotnet/gcc-ar-14.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar-14.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar-14.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar-14.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar-14.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar-14.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar-14.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar-14.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar-14.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar-14.2"
      - "E:/Node/gcc-ar-14.2.com"
      - "E:/Node/gcc-ar-14.2.exe"
      - "E:/Node/gcc-ar-14.2"
      - "C:/Program Files/CMake/bin/gcc-ar-14.2.com"
      - "C:/Program Files/CMake/bin/gcc-ar-14.2.exe"
      - "C:/Program Files/CMake/bin/gcc-ar-14.2"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-14.2"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-14.2"
      - "E:/Node/node_globle/gcc-ar-14.2.com"
      - "E:/Node/node_globle/gcc-ar-14.2.exe"
      - "E:/Node/node_globle/gcc-ar-14.2"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-14.2.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-14.2.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-14.2"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar-14.2.com"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar-14.2.exe"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar-14.2"
      - "E:/cursor/resources/app/bin/gcc-ar-14.2.com"
      - "E:/cursor/resources/app/bin/gcc-ar-14.2.exe"
      - "E:/cursor/resources/app/bin/gcc-ar-14.2"
      - "E:/msys64/ucrt64/bin/gcc-ar-14.com"
      - "E:/msys64/ucrt64/bin/gcc-ar-14.exe"
      - "E:/msys64/ucrt64/bin/gcc-ar-14"
      - "E:/msys64/usr/bin/gcc-ar-14.com"
      - "E:/msys64/usr/bin/gcc-ar-14.exe"
      - "E:/msys64/usr/bin/gcc-ar-14"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-14.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-14.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-14"
      - "C:/Windows/System32/gcc-ar-14.com"
      - "C:/Windows/System32/gcc-ar-14.exe"
      - "C:/Windows/System32/gcc-ar-14"
      - "C:/Windows/gcc-ar-14.com"
      - "C:/Windows/gcc-ar-14.exe"
      - "C:/Windows/gcc-ar-14"
      - "C:/Windows/System32/wbem/gcc-ar-14.com"
      - "C:/Windows/System32/wbem/gcc-ar-14.exe"
      - "C:/Windows/System32/wbem/gcc-ar-14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-14"
      - "C:/Windows/System32/OpenSSH/gcc-ar-14.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-14.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-14"
      - "D:/Git/cmd/gcc-ar-14.com"
      - "D:/Git/cmd/gcc-ar-14.exe"
      - "D:/Git/cmd/gcc-ar-14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-14"
      - "C:/Program Files/dotnet/gcc-ar-14.com"
      - "C:/Program Files/dotnet/gcc-ar-14.exe"
      - "C:/Program Files/dotnet/gcc-ar-14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar-14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar-14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar-14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar-14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar-14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar-14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar-14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar-14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar-14"
      - "E:/Node/gcc-ar-14.com"
      - "E:/Node/gcc-ar-14.exe"
      - "E:/Node/gcc-ar-14"
      - "C:/Program Files/CMake/bin/gcc-ar-14.com"
      - "C:/Program Files/CMake/bin/gcc-ar-14.exe"
      - "C:/Program Files/CMake/bin/gcc-ar-14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-14"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-14"
      - "E:/Node/node_globle/gcc-ar-14.com"
      - "E:/Node/node_globle/gcc-ar-14.exe"
      - "E:/Node/node_globle/gcc-ar-14"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-14"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar-14.com"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar-14.exe"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar-14"
      - "E:/cursor/resources/app/bin/gcc-ar-14.com"
      - "E:/cursor/resources/app/bin/gcc-ar-14.exe"
      - "E:/cursor/resources/app/bin/gcc-ar-14"
      - "E:/msys64/ucrt64/bin/gcc-ar14.com"
      - "E:/msys64/ucrt64/bin/gcc-ar14.exe"
      - "E:/msys64/ucrt64/bin/gcc-ar14"
      - "E:/msys64/usr/bin/gcc-ar14.com"
      - "E:/msys64/usr/bin/gcc-ar14.exe"
      - "E:/msys64/usr/bin/gcc-ar14"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar14.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar14.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar14"
      - "C:/Windows/System32/gcc-ar14.com"
      - "C:/Windows/System32/gcc-ar14.exe"
      - "C:/Windows/System32/gcc-ar14"
      - "C:/Windows/gcc-ar14.com"
      - "C:/Windows/gcc-ar14.exe"
      - "C:/Windows/gcc-ar14"
      - "C:/Windows/System32/wbem/gcc-ar14.com"
      - "C:/Windows/System32/wbem/gcc-ar14.exe"
      - "C:/Windows/System32/wbem/gcc-ar14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar14"
      - "C:/Windows/System32/OpenSSH/gcc-ar14.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar14.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar14"
      - "D:/Git/cmd/gcc-ar14.com"
      - "D:/Git/cmd/gcc-ar14.exe"
      - "D:/Git/cmd/gcc-ar14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar14"
      - "C:/Program Files/dotnet/gcc-ar14.com"
      - "C:/Program Files/dotnet/gcc-ar14.exe"
      - "C:/Program Files/dotnet/gcc-ar14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar14"
      - "E:/Node/gcc-ar14.com"
      - "E:/Node/gcc-ar14.exe"
      - "E:/Node/gcc-ar14"
      - "C:/Program Files/CMake/bin/gcc-ar14.com"
      - "C:/Program Files/CMake/bin/gcc-ar14.exe"
      - "C:/Program Files/CMake/bin/gcc-ar14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar14"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar14"
      - "E:/Node/node_globle/gcc-ar14.com"
      - "E:/Node/node_globle/gcc-ar14.exe"
      - "E:/Node/node_globle/gcc-ar14"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar14"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar14.com"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar14.exe"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar14"
      - "E:/cursor/resources/app/bin/gcc-ar14.com"
      - "E:/cursor/resources/app/bin/gcc-ar14.exe"
      - "E:/cursor/resources/app/bin/gcc-ar14"
      - "E:/msys64/ucrt64/bin/gcc-ar.com"
    found: "E:/msys64/ucrt64/bin/gcc-ar.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-14.2"
      - "gcc-ranlib-14"
      - "gcc-ranlib14"
      - "gcc-ranlib"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/gcc-ranlib-14.2.com"
      - "E:/msys64/ucrt64/bin/gcc-ranlib-14.2.exe"
      - "E:/msys64/ucrt64/bin/gcc-ranlib-14.2"
      - "E:/msys64/usr/bin/gcc-ranlib-14.2.com"
      - "E:/msys64/usr/bin/gcc-ranlib-14.2.exe"
      - "E:/msys64/usr/bin/gcc-ranlib-14.2"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-14.2.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-14.2.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-14.2"
      - "C:/Windows/System32/gcc-ranlib-14.2.com"
      - "C:/Windows/System32/gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/gcc-ranlib-14.2"
      - "C:/Windows/gcc-ranlib-14.2.com"
      - "C:/Windows/gcc-ranlib-14.2.exe"
      - "C:/Windows/gcc-ranlib-14.2"
      - "C:/Windows/System32/wbem/gcc-ranlib-14.2.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-14.2"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-14.2.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-14.2"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-14.2.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-14.2"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-14.2.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-14.2.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-14.2"
      - "D:/Git/cmd/gcc-ranlib-14.2.com"
      - "D:/Git/cmd/gcc-ranlib-14.2.exe"
      - "D:/Git/cmd/gcc-ranlib-14.2"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-14.2.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-14.2.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-14.2"
      - "C:/Program Files/dotnet/gcc-ranlib-14.2.com"
      - "C:/Program Files/dotnet/gcc-ranlib-14.2.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-14.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib-14.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib-14.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib-14.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib-14.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib-14.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib-14.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib-14.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib-14.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib-14.2"
      - "E:/Node/gcc-ranlib-14.2.com"
      - "E:/Node/gcc-ranlib-14.2.exe"
      - "E:/Node/gcc-ranlib-14.2"
      - "C:/Program Files/CMake/bin/gcc-ranlib-14.2.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib-14.2.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib-14.2"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-14.2"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-14.2"
      - "E:/Node/node_globle/gcc-ranlib-14.2.com"
      - "E:/Node/node_globle/gcc-ranlib-14.2.exe"
      - "E:/Node/node_globle/gcc-ranlib-14.2"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-14.2.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-14.2.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-14.2"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib-14.2.com"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib-14.2.exe"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib-14.2"
      - "E:/cursor/resources/app/bin/gcc-ranlib-14.2.com"
      - "E:/cursor/resources/app/bin/gcc-ranlib-14.2.exe"
      - "E:/cursor/resources/app/bin/gcc-ranlib-14.2"
      - "E:/msys64/ucrt64/bin/gcc-ranlib-14.com"
      - "E:/msys64/ucrt64/bin/gcc-ranlib-14.exe"
      - "E:/msys64/ucrt64/bin/gcc-ranlib-14"
      - "E:/msys64/usr/bin/gcc-ranlib-14.com"
      - "E:/msys64/usr/bin/gcc-ranlib-14.exe"
      - "E:/msys64/usr/bin/gcc-ranlib-14"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-14.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-14.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-14"
      - "C:/Windows/System32/gcc-ranlib-14.com"
      - "C:/Windows/System32/gcc-ranlib-14.exe"
      - "C:/Windows/System32/gcc-ranlib-14"
      - "C:/Windows/gcc-ranlib-14.com"
      - "C:/Windows/gcc-ranlib-14.exe"
      - "C:/Windows/gcc-ranlib-14"
      - "C:/Windows/System32/wbem/gcc-ranlib-14.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-14.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-14"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-14.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-14.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-14"
      - "D:/Git/cmd/gcc-ranlib-14.com"
      - "D:/Git/cmd/gcc-ranlib-14.exe"
      - "D:/Git/cmd/gcc-ranlib-14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-14"
      - "C:/Program Files/dotnet/gcc-ranlib-14.com"
      - "C:/Program Files/dotnet/gcc-ranlib-14.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib-14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib-14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib-14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib-14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib-14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib-14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib-14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib-14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib-14"
      - "E:/Node/gcc-ranlib-14.com"
      - "E:/Node/gcc-ranlib-14.exe"
      - "E:/Node/gcc-ranlib-14"
      - "C:/Program Files/CMake/bin/gcc-ranlib-14.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib-14.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib-14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-14"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-14"
      - "E:/Node/node_globle/gcc-ranlib-14.com"
      - "E:/Node/node_globle/gcc-ranlib-14.exe"
      - "E:/Node/node_globle/gcc-ranlib-14"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-14"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib-14.com"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib-14.exe"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib-14"
      - "E:/cursor/resources/app/bin/gcc-ranlib-14.com"
      - "E:/cursor/resources/app/bin/gcc-ranlib-14.exe"
      - "E:/cursor/resources/app/bin/gcc-ranlib-14"
      - "E:/msys64/ucrt64/bin/gcc-ranlib14.com"
      - "E:/msys64/ucrt64/bin/gcc-ranlib14.exe"
      - "E:/msys64/ucrt64/bin/gcc-ranlib14"
      - "E:/msys64/usr/bin/gcc-ranlib14.com"
      - "E:/msys64/usr/bin/gcc-ranlib14.exe"
      - "E:/msys64/usr/bin/gcc-ranlib14"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib14.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib14.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib14"
      - "C:/Windows/System32/gcc-ranlib14.com"
      - "C:/Windows/System32/gcc-ranlib14.exe"
      - "C:/Windows/System32/gcc-ranlib14"
      - "C:/Windows/gcc-ranlib14.com"
      - "C:/Windows/gcc-ranlib14.exe"
      - "C:/Windows/gcc-ranlib14"
      - "C:/Windows/System32/wbem/gcc-ranlib14.com"
      - "C:/Windows/System32/wbem/gcc-ranlib14.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib14"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib14.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib14.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib14"
      - "D:/Git/cmd/gcc-ranlib14.com"
      - "D:/Git/cmd/gcc-ranlib14.exe"
      - "D:/Git/cmd/gcc-ranlib14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib14"
      - "C:/Program Files/dotnet/gcc-ranlib14.com"
      - "C:/Program Files/dotnet/gcc-ranlib14.exe"
      - "C:/Program Files/dotnet/gcc-ranlib14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib14"
      - "E:/Node/gcc-ranlib14.com"
      - "E:/Node/gcc-ranlib14.exe"
      - "E:/Node/gcc-ranlib14"
      - "C:/Program Files/CMake/bin/gcc-ranlib14.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib14.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib14"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib14"
      - "E:/Node/node_globle/gcc-ranlib14.com"
      - "E:/Node/node_globle/gcc-ranlib14.exe"
      - "E:/Node/node_globle/gcc-ranlib14"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib14"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib14.com"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib14.exe"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib14"
      - "E:/cursor/resources/app/bin/gcc-ranlib14.com"
      - "E:/cursor/resources/app/bin/gcc-ranlib14.exe"
      - "E:/cursor/resources/app/bin/gcc-ranlib14"
      - "E:/msys64/ucrt64/bin/gcc-ranlib.com"
    found: "E:/msys64/ucrt64/bin/gcc-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: E:/msys64/ucrt64/bin/g++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is GNU, found in:
        D:/pet/CODE/图形/HHMM/Hw2/build/CMakeFiles/4.1.0/CompilerIdCXX/a.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-14.2"
      - "gcc-ar-14"
      - "gcc-ar14"
      - "gcc-ar"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/gcc-ar-14.2.com"
      - "E:/msys64/ucrt64/bin/gcc-ar-14.2.exe"
      - "E:/msys64/ucrt64/bin/gcc-ar-14.2"
      - "E:/msys64/usr/bin/gcc-ar-14.2.com"
      - "E:/msys64/usr/bin/gcc-ar-14.2.exe"
      - "E:/msys64/usr/bin/gcc-ar-14.2"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-14.2.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-14.2.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-14.2"
      - "C:/Windows/System32/gcc-ar-14.2.com"
      - "C:/Windows/System32/gcc-ar-14.2.exe"
      - "C:/Windows/System32/gcc-ar-14.2"
      - "C:/Windows/gcc-ar-14.2.com"
      - "C:/Windows/gcc-ar-14.2.exe"
      - "C:/Windows/gcc-ar-14.2"
      - "C:/Windows/System32/wbem/gcc-ar-14.2.com"
      - "C:/Windows/System32/wbem/gcc-ar-14.2.exe"
      - "C:/Windows/System32/wbem/gcc-ar-14.2"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-14.2.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-14.2.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-14.2"
      - "C:/Windows/System32/OpenSSH/gcc-ar-14.2.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-14.2.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-14.2"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-14.2.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-14.2.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-14.2"
      - "D:/Git/cmd/gcc-ar-14.2.com"
      - "D:/Git/cmd/gcc-ar-14.2.exe"
      - "D:/Git/cmd/gcc-ar-14.2"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-14.2.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-14.2.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-14.2"
      - "C:/Program Files/dotnet/gcc-ar-14.2.com"
      - "C:/Program Files/dotnet/gcc-ar-14.2.exe"
      - "C:/Program Files/dotnet/gcc-ar-14.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar-14.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar-14.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar-14.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar-14.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar-14.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar-14.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar-14.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar-14.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar-14.2"
      - "E:/Node/gcc-ar-14.2.com"
      - "E:/Node/gcc-ar-14.2.exe"
      - "E:/Node/gcc-ar-14.2"
      - "C:/Program Files/CMake/bin/gcc-ar-14.2.com"
      - "C:/Program Files/CMake/bin/gcc-ar-14.2.exe"
      - "C:/Program Files/CMake/bin/gcc-ar-14.2"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-14.2"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-14.2"
      - "E:/Node/node_globle/gcc-ar-14.2.com"
      - "E:/Node/node_globle/gcc-ar-14.2.exe"
      - "E:/Node/node_globle/gcc-ar-14.2"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-14.2.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-14.2.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-14.2"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar-14.2.com"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar-14.2.exe"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar-14.2"
      - "E:/cursor/resources/app/bin/gcc-ar-14.2.com"
      - "E:/cursor/resources/app/bin/gcc-ar-14.2.exe"
      - "E:/cursor/resources/app/bin/gcc-ar-14.2"
      - "E:/msys64/ucrt64/bin/gcc-ar-14.com"
      - "E:/msys64/ucrt64/bin/gcc-ar-14.exe"
      - "E:/msys64/ucrt64/bin/gcc-ar-14"
      - "E:/msys64/usr/bin/gcc-ar-14.com"
      - "E:/msys64/usr/bin/gcc-ar-14.exe"
      - "E:/msys64/usr/bin/gcc-ar-14"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-14.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-14.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-14"
      - "C:/Windows/System32/gcc-ar-14.com"
      - "C:/Windows/System32/gcc-ar-14.exe"
      - "C:/Windows/System32/gcc-ar-14"
      - "C:/Windows/gcc-ar-14.com"
      - "C:/Windows/gcc-ar-14.exe"
      - "C:/Windows/gcc-ar-14"
      - "C:/Windows/System32/wbem/gcc-ar-14.com"
      - "C:/Windows/System32/wbem/gcc-ar-14.exe"
      - "C:/Windows/System32/wbem/gcc-ar-14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-14"
      - "C:/Windows/System32/OpenSSH/gcc-ar-14.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-14.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-14"
      - "D:/Git/cmd/gcc-ar-14.com"
      - "D:/Git/cmd/gcc-ar-14.exe"
      - "D:/Git/cmd/gcc-ar-14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-14"
      - "C:/Program Files/dotnet/gcc-ar-14.com"
      - "C:/Program Files/dotnet/gcc-ar-14.exe"
      - "C:/Program Files/dotnet/gcc-ar-14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar-14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar-14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar-14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar-14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar-14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar-14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar-14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar-14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar-14"
      - "E:/Node/gcc-ar-14.com"
      - "E:/Node/gcc-ar-14.exe"
      - "E:/Node/gcc-ar-14"
      - "C:/Program Files/CMake/bin/gcc-ar-14.com"
      - "C:/Program Files/CMake/bin/gcc-ar-14.exe"
      - "C:/Program Files/CMake/bin/gcc-ar-14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-14"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-14"
      - "E:/Node/node_globle/gcc-ar-14.com"
      - "E:/Node/node_globle/gcc-ar-14.exe"
      - "E:/Node/node_globle/gcc-ar-14"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-14"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar-14.com"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar-14.exe"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar-14"
      - "E:/cursor/resources/app/bin/gcc-ar-14.com"
      - "E:/cursor/resources/app/bin/gcc-ar-14.exe"
      - "E:/cursor/resources/app/bin/gcc-ar-14"
      - "E:/msys64/ucrt64/bin/gcc-ar14.com"
      - "E:/msys64/ucrt64/bin/gcc-ar14.exe"
      - "E:/msys64/ucrt64/bin/gcc-ar14"
      - "E:/msys64/usr/bin/gcc-ar14.com"
      - "E:/msys64/usr/bin/gcc-ar14.exe"
      - "E:/msys64/usr/bin/gcc-ar14"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar14.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar14.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar14"
      - "C:/Windows/System32/gcc-ar14.com"
      - "C:/Windows/System32/gcc-ar14.exe"
      - "C:/Windows/System32/gcc-ar14"
      - "C:/Windows/gcc-ar14.com"
      - "C:/Windows/gcc-ar14.exe"
      - "C:/Windows/gcc-ar14"
      - "C:/Windows/System32/wbem/gcc-ar14.com"
      - "C:/Windows/System32/wbem/gcc-ar14.exe"
      - "C:/Windows/System32/wbem/gcc-ar14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar14"
      - "C:/Windows/System32/OpenSSH/gcc-ar14.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar14.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar14"
      - "D:/Git/cmd/gcc-ar14.com"
      - "D:/Git/cmd/gcc-ar14.exe"
      - "D:/Git/cmd/gcc-ar14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar14"
      - "C:/Program Files/dotnet/gcc-ar14.com"
      - "C:/Program Files/dotnet/gcc-ar14.exe"
      - "C:/Program Files/dotnet/gcc-ar14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ar14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ar14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ar14"
      - "E:/Node/gcc-ar14.com"
      - "E:/Node/gcc-ar14.exe"
      - "E:/Node/gcc-ar14"
      - "C:/Program Files/CMake/bin/gcc-ar14.com"
      - "C:/Program Files/CMake/bin/gcc-ar14.exe"
      - "C:/Program Files/CMake/bin/gcc-ar14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar14"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar14"
      - "E:/Node/node_globle/gcc-ar14.com"
      - "E:/Node/node_globle/gcc-ar14.exe"
      - "E:/Node/node_globle/gcc-ar14"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar14"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar14.com"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar14.exe"
      - "E:/opencv/build/x64/vc15/bin/gcc-ar14"
      - "E:/cursor/resources/app/bin/gcc-ar14.com"
      - "E:/cursor/resources/app/bin/gcc-ar14.exe"
      - "E:/cursor/resources/app/bin/gcc-ar14"
      - "E:/msys64/ucrt64/bin/gcc-ar.com"
    found: "E:/msys64/ucrt64/bin/gcc-ar.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-14.2"
      - "gcc-ranlib-14"
      - "gcc-ranlib14"
      - "gcc-ranlib"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/gcc-ranlib-14.2.com"
      - "E:/msys64/ucrt64/bin/gcc-ranlib-14.2.exe"
      - "E:/msys64/ucrt64/bin/gcc-ranlib-14.2"
      - "E:/msys64/usr/bin/gcc-ranlib-14.2.com"
      - "E:/msys64/usr/bin/gcc-ranlib-14.2.exe"
      - "E:/msys64/usr/bin/gcc-ranlib-14.2"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-14.2.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-14.2.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-14.2"
      - "C:/Windows/System32/gcc-ranlib-14.2.com"
      - "C:/Windows/System32/gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/gcc-ranlib-14.2"
      - "C:/Windows/gcc-ranlib-14.2.com"
      - "C:/Windows/gcc-ranlib-14.2.exe"
      - "C:/Windows/gcc-ranlib-14.2"
      - "C:/Windows/System32/wbem/gcc-ranlib-14.2.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-14.2"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-14.2.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-14.2"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-14.2.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-14.2"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-14.2.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-14.2.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-14.2"
      - "D:/Git/cmd/gcc-ranlib-14.2.com"
      - "D:/Git/cmd/gcc-ranlib-14.2.exe"
      - "D:/Git/cmd/gcc-ranlib-14.2"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-14.2.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-14.2.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-14.2"
      - "C:/Program Files/dotnet/gcc-ranlib-14.2.com"
      - "C:/Program Files/dotnet/gcc-ranlib-14.2.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-14.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib-14.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib-14.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib-14.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib-14.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib-14.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib-14.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib-14.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib-14.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib-14.2"
      - "E:/Node/gcc-ranlib-14.2.com"
      - "E:/Node/gcc-ranlib-14.2.exe"
      - "E:/Node/gcc-ranlib-14.2"
      - "C:/Program Files/CMake/bin/gcc-ranlib-14.2.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib-14.2.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib-14.2"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-14.2"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-14.2"
      - "E:/Node/node_globle/gcc-ranlib-14.2.com"
      - "E:/Node/node_globle/gcc-ranlib-14.2.exe"
      - "E:/Node/node_globle/gcc-ranlib-14.2"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-14.2.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-14.2.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-14.2"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib-14.2.com"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib-14.2.exe"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib-14.2"
      - "E:/cursor/resources/app/bin/gcc-ranlib-14.2.com"
      - "E:/cursor/resources/app/bin/gcc-ranlib-14.2.exe"
      - "E:/cursor/resources/app/bin/gcc-ranlib-14.2"
      - "E:/msys64/ucrt64/bin/gcc-ranlib-14.com"
      - "E:/msys64/ucrt64/bin/gcc-ranlib-14.exe"
      - "E:/msys64/ucrt64/bin/gcc-ranlib-14"
      - "E:/msys64/usr/bin/gcc-ranlib-14.com"
      - "E:/msys64/usr/bin/gcc-ranlib-14.exe"
      - "E:/msys64/usr/bin/gcc-ranlib-14"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-14.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-14.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-14"
      - "C:/Windows/System32/gcc-ranlib-14.com"
      - "C:/Windows/System32/gcc-ranlib-14.exe"
      - "C:/Windows/System32/gcc-ranlib-14"
      - "C:/Windows/gcc-ranlib-14.com"
      - "C:/Windows/gcc-ranlib-14.exe"
      - "C:/Windows/gcc-ranlib-14"
      - "C:/Windows/System32/wbem/gcc-ranlib-14.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-14.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-14"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-14.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-14.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-14"
      - "D:/Git/cmd/gcc-ranlib-14.com"
      - "D:/Git/cmd/gcc-ranlib-14.exe"
      - "D:/Git/cmd/gcc-ranlib-14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-14"
      - "C:/Program Files/dotnet/gcc-ranlib-14.com"
      - "C:/Program Files/dotnet/gcc-ranlib-14.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib-14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib-14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib-14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib-14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib-14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib-14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib-14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib-14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib-14"
      - "E:/Node/gcc-ranlib-14.com"
      - "E:/Node/gcc-ranlib-14.exe"
      - "E:/Node/gcc-ranlib-14"
      - "C:/Program Files/CMake/bin/gcc-ranlib-14.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib-14.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib-14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-14"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-14"
      - "E:/Node/node_globle/gcc-ranlib-14.com"
      - "E:/Node/node_globle/gcc-ranlib-14.exe"
      - "E:/Node/node_globle/gcc-ranlib-14"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-14"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib-14.com"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib-14.exe"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib-14"
      - "E:/cursor/resources/app/bin/gcc-ranlib-14.com"
      - "E:/cursor/resources/app/bin/gcc-ranlib-14.exe"
      - "E:/cursor/resources/app/bin/gcc-ranlib-14"
      - "E:/msys64/ucrt64/bin/gcc-ranlib14.com"
      - "E:/msys64/ucrt64/bin/gcc-ranlib14.exe"
      - "E:/msys64/ucrt64/bin/gcc-ranlib14"
      - "E:/msys64/usr/bin/gcc-ranlib14.com"
      - "E:/msys64/usr/bin/gcc-ranlib14.exe"
      - "E:/msys64/usr/bin/gcc-ranlib14"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib14.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib14.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib14"
      - "C:/Windows/System32/gcc-ranlib14.com"
      - "C:/Windows/System32/gcc-ranlib14.exe"
      - "C:/Windows/System32/gcc-ranlib14"
      - "C:/Windows/gcc-ranlib14.com"
      - "C:/Windows/gcc-ranlib14.exe"
      - "C:/Windows/gcc-ranlib14"
      - "C:/Windows/System32/wbem/gcc-ranlib14.com"
      - "C:/Windows/System32/wbem/gcc-ranlib14.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib14"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib14.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib14.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib14"
      - "D:/Git/cmd/gcc-ranlib14.com"
      - "D:/Git/cmd/gcc-ranlib14.exe"
      - "D:/Git/cmd/gcc-ranlib14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib14"
      - "C:/Program Files/dotnet/gcc-ranlib14.com"
      - "C:/Program Files/dotnet/gcc-ranlib14.exe"
      - "C:/Program Files/dotnet/gcc-ranlib14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/gcc-ranlib14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/gcc-ranlib14"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib14.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib14.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/gcc-ranlib14"
      - "E:/Node/gcc-ranlib14.com"
      - "E:/Node/gcc-ranlib14.exe"
      - "E:/Node/gcc-ranlib14"
      - "C:/Program Files/CMake/bin/gcc-ranlib14.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib14.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib14"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib14"
      - "E:/Node/node_globle/gcc-ranlib14.com"
      - "E:/Node/node_globle/gcc-ranlib14.exe"
      - "E:/Node/node_globle/gcc-ranlib14"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib14"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib14.com"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib14.exe"
      - "E:/opencv/build/x64/vc15/bin/gcc-ranlib14"
      - "E:/cursor/resources/app/bin/gcc-ranlib14.com"
      - "E:/cursor/resources/app/bin/gcc-ranlib14.exe"
      - "E:/cursor/resources/app/bin/gcc-ranlib14"
      - "E:/msys64/ucrt64/bin/gcc-ranlib.com"
    found: "E:/msys64/ucrt64/bin/gcc-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU.cmake:167 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU-C.cmake:2 (__windows_compiler_gnu)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCInformation.cmake:48 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "windres"
      - "windres"
    candidate_directories:
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/usr/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc15/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/msys64/ucrt64/bin/"
      - "E:/msys64/ucrt64/sbin/"
      - "E:/msys64/ucrt64/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/Rasterizer/bin/"
      - "C:/Program Files (x86)/Rasterizer/sbin/"
      - "C:/Program Files (x86)/Rasterizer/"
    searched_directories:
      - "E:/msys64/ucrt64/bin/windres.com"
    found: "E:/msys64/ucrt64/bin/windres.exe"
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/Rasterizer"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "E:/msys64/ucrt64"
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/Rasterizer"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-odsyw5"
      binary: "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-odsyw5"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/pet/CODE/图形/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-odsyw5'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 E:/msys64/ucrt64/bin/mingw32-make.exe -f Makefile cmTC_78812/fast
        E:/msys64/ucrt64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_78812.dir\\build.make CMakeFiles/cmTC_78812.dir/build
        mingw32-make[1]: Entering directory 'D:/pet/CODE/图形/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-odsyw5'
        Building C object CMakeFiles/cmTC_78812.dir/CMakeCCompilerABI.c.obj
        E:\\msys64\\ucrt64\\bin\\gcc.exe   -v -o CMakeFiles\\cmTC_78812.dir\\CMakeCCompilerABI.c.obj -c "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
        Using built-in specs.
        COLLECT_GCC=E:\\msys64\\ucrt64\\bin\\gcc.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-14.2.0/configure --prefix=/ucrt64 --with-local-prefix=/ucrt64/local --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --with-native-system-header-dir=/ucrt64/include --libexecdir=/ucrt64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,rust,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/ucrt64 --with-mpfr=/ucrt64 --with-mpc=/ucrt64 --with-isl=/ucrt64 --with-pkgversion='Rev2, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.0 (Rev2, Built by MSYS2 project) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_78812.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_78812.dir\\'
         E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/cc1.exe -quiet -v -iprefix E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/ -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_78812.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccdOkxSg.s
        GNU C17 (Rev2, Built by MSYS2 project) version 14.2.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 14.2.0, GMP version 6.3.0, MPFR version 4.2.1, MPC version 1.3.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include"
        ignoring duplicate directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include"
        ignoring nonexistent directory "D:/a/msys64/ucrt64/include"
        ignoring nonexistent directory "/ucrt64/include"
        ignoring duplicate directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed"
        ignoring nonexistent directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "D:/a/msys64/ucrt64/include"
        #include "..." search starts here:
        #include <...> search starts here:
         E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include
         E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include
         E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed
        End of search list.
        Compiler executable checksum: bfed5edf6a89cec0a35941200a765959
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_78812.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_78812.dir\\'
         E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_78812.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccdOkxSg.s
        GNU assembler version 2.43.1 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.43.1
        COMPILER_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/;E:/msys64/ucrt64/bin/../lib/gcc/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/;E:/msys64/ucrt64/bin/../lib/gcc/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_78812.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_78812.dir\\CMakeCCompilerABI.c.'
        Linking C executable cmTC_78812.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_78812.dir\\link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=E:\\msys64\\ucrt64\\bin\\gcc.exe
        COLLECT_LTO_WRAPPER=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-14.2.0/configure --prefix=/ucrt64 --with-local-prefix=/ucrt64/local --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --with-native-system-header-dir=/ucrt64/include --libexecdir=/ucrt64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,rust,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/ucrt64 --with-mpfr=/ucrt64 --with-mpc=/ucrt64 --with-isl=/ucrt64 --with-pkgversion='Rev2, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.0 (Rev2, Built by MSYS2 project) 
        COMPILER_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/;E:/msys64/ucrt64/bin/../lib/gcc/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/;E:/msys64/ucrt64/bin/../lib/gcc/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_78812.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_78812.'
         E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/collect2.exe -plugin E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll -plugin-opt=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccY9aZ3x.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_78812.exe E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/crt2.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0 -LE:/msys64/ucrt64/bin/../lib/gcc -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_78812.dir/objects.a --no-whole-archive --out-implib libcmTC_78812.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/default-manifest.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o
        collect2 version 14.2.0
        E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll -plugin-opt=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccY9aZ3x.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_78812.exe E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/crt2.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0 -LE:/msys64/ucrt64/bin/../lib/gcc -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_78812.dir/objects.a --no-whole-archive --out-implib libcmTC_78812.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/default-manifest.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o
        GNU ld (GNU Binutils) 2.43.1
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_78812.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_78812.'
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_78812.dir/objects.a
        E:\\msys64\\ucrt64\\bin\\ar.exe qc CMakeFiles\\cmTC_78812.dir/objects.a @CMakeFiles\\cmTC_78812.dir\\objects1.rsp
        E:\\msys64\\ucrt64\\bin\\gcc.exe  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_78812.dir/objects.a -Wl,--no-whole-archive -o cmTC_78812.exe -Wl,--out-implib,libcmTC_78812.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        mingw32-make[1]: Leaving directory 'D:/pet/CODE/图形/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-odsyw5'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include]
          add: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include]
          add: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed]
        end of search list found
        collapse include dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include] ==> [E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/include]
        collapse include dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include] ==> [E:/msys64/ucrt64/include]
        collapse include dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed] ==> [E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed]
        implicit include dirs: [E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/include;E:/msys64/ucrt64/include;E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'D:/pet/CODE/图形/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-odsyw5']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 E:/msys64/ucrt64/bin/mingw32-make.exe -f Makefile cmTC_78812/fast]
        ignore line: [E:/msys64/ucrt64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_78812.dir\\build.make CMakeFiles/cmTC_78812.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'D:/pet/CODE/图形/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-odsyw5']
        ignore line: [Building C object CMakeFiles/cmTC_78812.dir/CMakeCCompilerABI.c.obj]
        ignore line: [E:\\msys64\\ucrt64\\bin\\gcc.exe   -v -o CMakeFiles\\cmTC_78812.dir\\CMakeCCompilerABI.c.obj -c "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=E:\\msys64\\ucrt64\\bin\\gcc.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-14.2.0/configure --prefix=/ucrt64 --with-local-prefix=/ucrt64/local --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --with-native-system-header-dir=/ucrt64/include --libexecdir=/ucrt64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,rust,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/ucrt64 --with-mpfr=/ucrt64 --with-mpc=/ucrt64 --with-isl=/ucrt64 --with-pkgversion='Rev2, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.0 (Rev2  Built by MSYS2 project) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_78812.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_78812.dir\\']
        ignore line: [ E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/cc1.exe -quiet -v -iprefix E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/ -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_78812.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccdOkxSg.s]
        ignore line: [GNU C17 (Rev2  Built by MSYS2 project) version 14.2.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 14.2.0  GMP version 6.3.0  MPFR version 4.2.1  MPC version 1.3.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring duplicate directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include"]
        ignore line: [ignoring nonexistent directory "D:/a/msys64/ucrt64/include"]
        ignore line: [ignoring nonexistent directory "/ucrt64/include"]
        ignore line: [ignoring duplicate directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed"]
        ignore line: [ignoring nonexistent directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "D:/a/msys64/ucrt64/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include]
        ignore line: [ E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include]
        ignore line: [ E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: bfed5edf6a89cec0a35941200a765959]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_78812.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_78812.dir\\']
        ignore line: [ E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_78812.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccdOkxSg.s]
        ignore line: [GNU assembler version 2.43.1 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.43.1]
        ignore line: [COMPILER_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_78812.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_78812.dir\\CMakeCCompilerABI.c.']
        ignore line: [Linking C executable cmTC_78812.exe]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_78812.dir\\link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=E:\\msys64\\ucrt64\\bin\\gcc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-14.2.0/configure --prefix=/ucrt64 --with-local-prefix=/ucrt64/local --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --with-native-system-header-dir=/ucrt64/include --libexecdir=/ucrt64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,rust,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/ucrt64 --with-mpfr=/ucrt64 --with-mpc=/ucrt64 --with-isl=/ucrt64 --with-pkgversion='Rev2, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.0 (Rev2  Built by MSYS2 project) ]
        ignore line: [COMPILER_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_78812.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_78812.']
        link line: [ E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/collect2.exe -plugin E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll -plugin-opt=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccY9aZ3x.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_78812.exe E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/crt2.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0 -LE:/msys64/ucrt64/bin/../lib/gcc -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_78812.dir/objects.a --no-whole-archive --out-implib libcmTC_78812.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/default-manifest.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
          arg [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccY9aZ3x.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_78812.exe] ==> ignore
          arg [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/crt2.o] ==> obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/crt2.o]
          arg [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o] ==> obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o]
          arg [-LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0] ==> dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0]
          arg [-LE:/msys64/ucrt64/bin/../lib/gcc] ==> dir [E:/msys64/ucrt64/bin/../lib/gcc]
          arg [-LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib] ==> dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib]
          arg [-LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib] ==> dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../..] ==> dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_78812.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_78812.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/default-manifest.o] ==> obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/default-manifest.o]
          arg [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o] ==> obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
        ignore line: [collect2 version 14.2.0]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll -plugin-opt=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccY9aZ3x.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_78812.exe E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/crt2.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0 -LE:/msys64/ucrt64/bin/../lib/gcc -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_78812.dir/objects.a --no-whole-archive --out-implib libcmTC_78812.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/default-manifest.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
        linker tool for 'C': E:/msys64/ucrt64/x86_64-w64-mingw32/bin/ld.exe
        remove lib [gcc_eh]
        remove lib [msvcrt]
        remove lib [gcc_eh]
        remove lib [msvcrt]
        collapse obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/crt2.o] ==> [E:/msys64/ucrt64/lib/crt2.o]
        collapse obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o] ==> [E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o]
        collapse obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/default-manifest.o] ==> [E:/msys64/ucrt64/lib/default-manifest.o]
        collapse obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o] ==> [E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
        collapse library dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0] ==> [E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0]
        collapse library dir [E:/msys64/ucrt64/bin/../lib/gcc] ==> [E:/msys64/ucrt64/lib/gcc]
        collapse library dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [E:/msys64/ucrt64/x86_64-w64-mingw32/lib]
        collapse library dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib] ==> [E:/msys64/ucrt64/lib]
        collapse library dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib] ==> [E:/msys64/ucrt64/x86_64-w64-mingw32/lib]
        collapse library dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../..] ==> [E:/msys64/ucrt64/lib]
        implicit libs: [mingw32;gcc;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc;mingwex;kernel32]
        implicit objs: [E:/msys64/ucrt64/lib/crt2.o;E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o;E:/msys64/ucrt64/lib/default-manifest.o;E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
        implicit dirs: [E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0;E:/msys64/ucrt64/lib/gcc;E:/msys64/ucrt64/x86_64-w64-mingw32/lib;E:/msys64/ucrt64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "E:/msys64/ucrt64/x86_64-w64-mingw32/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.43.1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-4lkejw"
      binary: "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-4lkejw"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/pet/CODE/图形/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-4lkejw'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 E:/msys64/ucrt64/bin/mingw32-make.exe -f Makefile cmTC_0bb3d/fast
        E:/msys64/ucrt64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_0bb3d.dir\\build.make CMakeFiles/cmTC_0bb3d.dir/build
        mingw32-make[1]: Entering directory 'D:/pet/CODE/图形/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-4lkejw'
        Building CXX object CMakeFiles/cmTC_0bb3d.dir/CMakeCXXCompilerABI.cpp.obj
        E:\\msys64\\ucrt64\\bin\\g++.exe   -v -o CMakeFiles\\cmTC_0bb3d.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
        Using built-in specs.
        COLLECT_GCC=E:\\msys64\\ucrt64\\bin\\g++.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-14.2.0/configure --prefix=/ucrt64 --with-local-prefix=/ucrt64/local --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --with-native-system-header-dir=/ucrt64/include --libexecdir=/ucrt64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,rust,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/ucrt64 --with-mpfr=/ucrt64 --with-mpc=/ucrt64 --with-isl=/ucrt64 --with-pkgversion='Rev2, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.0 (Rev2, Built by MSYS2 project) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_0bb3d.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_0bb3d.dir\\'
         E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/cc1plus.exe -quiet -v -iprefix E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/ -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles\\cmTC_0bb3d.dir\\ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cca8Myd2.s
        GNU C++17 (Rev2, Built by MSYS2 project) version 14.2.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 14.2.0, GMP version 6.3.0, MPFR version 4.2.1, MPC version 1.3.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include"
        ignoring duplicate directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0"
        ignoring duplicate directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/x86_64-w64-mingw32"
        ignoring duplicate directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/backward"
        ignoring duplicate directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include"
        ignoring nonexistent directory "D:/a/msys64/ucrt64/include"
        ignoring nonexistent directory "/ucrt64/include"
        ignoring duplicate directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed"
        ignoring nonexistent directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "D:/a/msys64/ucrt64/include"
        #include "..." search starts here:
        #include <...> search starts here:
         E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0
         E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/x86_64-w64-mingw32
         E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/backward
         E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include
         E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include
         E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed
        End of search list.
        Compiler executable checksum: 0ae76c501e8dc149b84d2c95ded0e2f3
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_0bb3d.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_0bb3d.dir\\'
         E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_0bb3d.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cca8Myd2.s
        GNU assembler version 2.43.1 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.43.1
        COMPILER_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/;E:/msys64/ucrt64/bin/../lib/gcc/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/;E:/msys64/ucrt64/bin/../lib/gcc/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_0bb3d.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_0bb3d.dir\\CMakeCXXCompilerABI.cpp.'
        Linking CXX executable cmTC_0bb3d.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_0bb3d.dir\\link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=E:\\msys64\\ucrt64\\bin\\g++.exe
        COLLECT_LTO_WRAPPER=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-14.2.0/configure --prefix=/ucrt64 --with-local-prefix=/ucrt64/local --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --with-native-system-header-dir=/ucrt64/include --libexecdir=/ucrt64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,rust,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/ucrt64 --with-mpfr=/ucrt64 --with-mpc=/ucrt64 --with-isl=/ucrt64 --with-pkgversion='Rev2, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.0 (Rev2, Built by MSYS2 project) 
        COMPILER_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/;E:/msys64/ucrt64/bin/../lib/gcc/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/;E:/msys64/ucrt64/bin/../lib/gcc/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/;E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_0bb3d.exe' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_0bb3d.'
         E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/collect2.exe -plugin E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll -plugin-opt=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccPe4cWb.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_0bb3d.exe E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/crt2.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0 -LE:/msys64/ucrt64/bin/../lib/gcc -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_0bb3d.dir/objects.a --no-whole-archive --out-implib libcmTC_0bb3d.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/default-manifest.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o
        collect2 version 14.2.0
        E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll -plugin-opt=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccPe4cWb.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_0bb3d.exe E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/crt2.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0 -LE:/msys64/ucrt64/bin/../lib/gcc -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_0bb3d.dir/objects.a --no-whole-archive --out-implib libcmTC_0bb3d.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/default-manifest.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o
        GNU ld (GNU Binutils) 2.43.1
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_0bb3d.exe' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_0bb3d.'
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_0bb3d.dir/objects.a
        E:\\msys64\\ucrt64\\bin\\ar.exe qc CMakeFiles\\cmTC_0bb3d.dir/objects.a @CMakeFiles\\cmTC_0bb3d.dir\\objects1.rsp
        E:\\msys64\\ucrt64\\bin\\g++.exe  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_0bb3d.dir/objects.a -Wl,--no-whole-archive -o cmTC_0bb3d.exe -Wl,--out-implib,libcmTC_0bb3d.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        mingw32-make[1]: Leaving directory 'D:/pet/CODE/图形/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-4lkejw'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0]
          add: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/x86_64-w64-mingw32]
          add: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/backward]
          add: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include]
          add: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include]
          add: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed]
        end of search list found
        collapse include dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0] ==> [E:/msys64/ucrt64/include/c++/14.2.0]
        collapse include dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/x86_64-w64-mingw32] ==> [E:/msys64/ucrt64/include/c++/14.2.0/x86_64-w64-mingw32]
        collapse include dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/backward] ==> [E:/msys64/ucrt64/include/c++/14.2.0/backward]
        collapse include dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include] ==> [E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/include]
        collapse include dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include] ==> [E:/msys64/ucrt64/include]
        collapse include dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed] ==> [E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed]
        implicit include dirs: [E:/msys64/ucrt64/include/c++/14.2.0;E:/msys64/ucrt64/include/c++/14.2.0/x86_64-w64-mingw32;E:/msys64/ucrt64/include/c++/14.2.0/backward;E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/include;E:/msys64/ucrt64/include;E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'D:/pet/CODE/图形/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-4lkejw']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 E:/msys64/ucrt64/bin/mingw32-make.exe -f Makefile cmTC_0bb3d/fast]
        ignore line: [E:/msys64/ucrt64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_0bb3d.dir\\build.make CMakeFiles/cmTC_0bb3d.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'D:/pet/CODE/图形/HHMM/Hw2/build/CMakeFiles/CMakeScratch/TryCompile-4lkejw']
        ignore line: [Building CXX object CMakeFiles/cmTC_0bb3d.dir/CMakeCXXCompilerABI.cpp.obj]
        ignore line: [E:\\msys64\\ucrt64\\bin\\g++.exe   -v -o CMakeFiles\\cmTC_0bb3d.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=E:\\msys64\\ucrt64\\bin\\g++.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-14.2.0/configure --prefix=/ucrt64 --with-local-prefix=/ucrt64/local --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --with-native-system-header-dir=/ucrt64/include --libexecdir=/ucrt64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,rust,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/ucrt64 --with-mpfr=/ucrt64 --with-mpc=/ucrt64 --with-isl=/ucrt64 --with-pkgversion='Rev2, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.0 (Rev2  Built by MSYS2 project) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_0bb3d.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_0bb3d.dir\\']
        ignore line: [ E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/cc1plus.exe -quiet -v -iprefix E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/ -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles\\cmTC_0bb3d.dir\\ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cca8Myd2.s]
        ignore line: [GNU C++17 (Rev2  Built by MSYS2 project) version 14.2.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 14.2.0  GMP version 6.3.0  MPFR version 4.2.1  MPC version 1.3.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring duplicate directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0"]
        ignore line: [ignoring duplicate directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/x86_64-w64-mingw32"]
        ignore line: [ignoring duplicate directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/backward"]
        ignore line: [ignoring duplicate directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include"]
        ignore line: [ignoring nonexistent directory "D:/a/msys64/ucrt64/include"]
        ignore line: [ignoring nonexistent directory "/ucrt64/include"]
        ignore line: [ignoring duplicate directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed"]
        ignore line: [ignoring nonexistent directory "E:/msys64/ucrt64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "D:/a/msys64/ucrt64/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0]
        ignore line: [ E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/x86_64-w64-mingw32]
        ignore line: [ E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include/c++/14.2.0/backward]
        ignore line: [ E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include]
        ignore line: [ E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../include]
        ignore line: [ E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/include-fixed]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: 0ae76c501e8dc149b84d2c95ded0e2f3]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_0bb3d.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_0bb3d.dir\\']
        ignore line: [ E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_0bb3d.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cca8Myd2.s]
        ignore line: [GNU assembler version 2.43.1 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.43.1]
        ignore line: [COMPILER_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_0bb3d.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_0bb3d.dir\\CMakeCXXCompilerABI.cpp.']
        ignore line: [Linking CXX executable cmTC_0bb3d.exe]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_0bb3d.dir\\link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=E:\\msys64\\ucrt64\\bin\\g++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-14.2.0/configure --prefix=/ucrt64 --with-local-prefix=/ucrt64/local --build=x86_64-w64-mingw32 --host=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --with-native-system-header-dir=/ucrt64/include --libexecdir=/ucrt64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,rust,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/ucrt64 --with-mpfr=/ucrt64 --with-mpc=/ucrt64 --with-isl=/ucrt64 --with-pkgversion='Rev2, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.0 (Rev2  Built by MSYS2 project) ]
        ignore line: [COMPILER_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_0bb3d.exe' '-shared-libgcc' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_0bb3d.']
        link line: [ E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/collect2.exe -plugin E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll -plugin-opt=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccPe4cWb.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_0bb3d.exe E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/crt2.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0 -LE:/msys64/ucrt64/bin/../lib/gcc -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_0bb3d.dir/objects.a --no-whole-archive --out-implib libcmTC_0bb3d.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/default-manifest.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
          arg [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccPe4cWb.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_0bb3d.exe] ==> ignore
          arg [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/crt2.o] ==> obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/crt2.o]
          arg [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o] ==> obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o]
          arg [-LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0] ==> dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0]
          arg [-LE:/msys64/ucrt64/bin/../lib/gcc] ==> dir [E:/msys64/ucrt64/bin/../lib/gcc]
          arg [-LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib] ==> dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib]
          arg [-LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib] ==> dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../..] ==> dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_0bb3d.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_0bb3d.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/default-manifest.o] ==> obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/default-manifest.o]
          arg [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o] ==> obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
        ignore line: [collect2 version 14.2.0]
        ignore line: [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/liblto_plugin.dll -plugin-opt=E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccPe4cWb.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_0bb3d.exe E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/crt2.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0 -LE:/msys64/ucrt64/bin/../lib/gcc -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib -LE:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_0bb3d.dir/objects.a --no-whole-archive --out-implib libcmTC_0bb3d.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/default-manifest.o E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
        linker tool for 'CXX': E:/msys64/ucrt64/x86_64-w64-mingw32/bin/ld.exe
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/crt2.o] ==> [E:/msys64/ucrt64/lib/crt2.o]
        collapse obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o] ==> [E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o]
        collapse obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib/default-manifest.o] ==> [E:/msys64/ucrt64/lib/default-manifest.o]
        collapse obj [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o] ==> [E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
        collapse library dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0] ==> [E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0]
        collapse library dir [E:/msys64/ucrt64/bin/../lib/gcc] ==> [E:/msys64/ucrt64/lib/gcc]
        collapse library dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [E:/msys64/ucrt64/x86_64-w64-mingw32/lib]
        collapse library dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../lib] ==> [E:/msys64/ucrt64/lib]
        collapse library dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../../../x86_64-w64-mingw32/lib] ==> [E:/msys64/ucrt64/x86_64-w64-mingw32/lib]
        collapse library dir [E:/msys64/ucrt64/bin/../lib/gcc/x86_64-w64-mingw32/14.2.0/../../..] ==> [E:/msys64/ucrt64/lib]
        implicit libs: [stdc++;mingw32;gcc_s;gcc;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc_s;gcc;mingwex;kernel32]
        implicit objs: [E:/msys64/ucrt64/lib/crt2.o;E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/crtbegin.o;E:/msys64/ucrt64/lib/default-manifest.o;E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0/crtend.o]
        implicit dirs: [E:/msys64/ucrt64/lib/gcc/x86_64-w64-mingw32/14.2.0;E:/msys64/ucrt64/lib/gcc;E:/msys64/ucrt64/x86_64-w64-mingw32/lib;E:/msys64/ucrt64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "E:/msys64/ucrt64/x86_64-w64-mingw32/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.43.1
  -
    kind: "find_package-v1"
    backtrace:
      - "CMakeLists.txt:12 (find_package)"
    name: "OpenCV"
    configs:
      -
        filename: "OpenCVConfig.cmake"
        kind: "cmake"
      -
        filename: "opencv-config.cmake"
        kind: "cmake"
    version_request:
      exact: false
    settings:
      required: "required_explicit"
      quiet: false
      global: false
      policy_scope: true
      bypass_provider: false
      names:
        - "OpenCV"
      search_paths:
        - "E:/OpenCV/opencv/build"
      path_suffixes:
        - ""
      paths:
        CMAKE_FIND_USE_CMAKE_PATH: false
        CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
        CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
        CMAKE_FIND_USE_INSTALL_PREFIX: true
        CMAKE_FIND_USE_PACKAGE_ROOT_PATH: true
        CMAKE_FIND_USE_CMAKE_PACKAGE_REGISTRY: true
        CMAKE_FIND_USE_SYSTEM_PACKAGE_REGISTRY: true
        CMAKE_FIND_ROOT_PATH_MODE: "BOTH"
    candidates:
      -
        path: "E:/OpenCV/opencv/build/OpenCVConfig.cmake"
        mode: "none?"
        reason: "no_exist"
      -
        path: "E:/OpenCV/opencv/build/opencv-config.cmake"
        mode: "none?"
        reason: "no_exist"
      -
        path: "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/CMakeFiles/pkgRedirects/OpenCVConfig.cmake"
        mode: "config"
        reason: "no_exist"
      -
        path: "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw2/build/CMakeFiles/pkgRedirects/opencv-config.cmake"
        mode: "config"
        reason: "no_exist"
    found: null
    search_context:
      ENV{PATH}:
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\ucrt64\\bin"
        - "E:\\msys64\\usr\\bin"
        - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler"
        - "C:\\WINDOWS\\system32"
        - "C:/WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:/WINDOWS/System32/WindowsPowerShell/v1.0/"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common"
        - "D:\\Git\\cmd"
        - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:/Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:/Node/node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:/opencv/build/x64/vc15/bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:/msys64/ucrt64/bin"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/Rasterizer"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "E:/msys64/ucrt64"
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/Rasterizer"
...
