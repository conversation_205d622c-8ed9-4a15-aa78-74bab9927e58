
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.19+164abd434
      鐢熸垚鍚姩鏃堕棿涓?2025/9/5 19:13:30銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\4.1.0\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdC.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        E:\\VS\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        E:\\VS\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\4.1.0\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=E:\\VS\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淒:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\4.1.0\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.41
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/4.1.0/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:103 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/mingw64/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.com"
    found: "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/mingw64/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.com"
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.exe"
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/lld-link.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/lld-link.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link"
      - "D:/Git/cmd/lld-link.com"
      - "D:/Git/cmd/lld-link.exe"
      - "D:/Git/cmd/lld-link"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/lld-link.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/lld-link.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/lld-link.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/lld-link.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/lld-link"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/lld-link.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/lld-link.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/lld-link"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/lld-link.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/lld-link.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/lld-link"
      - "E:/Node/lld-link.com"
      - "E:/Node/lld-link.exe"
      - "E:/Node/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link"
      - "E:/Node/node_globle/lld-link.com"
      - "E:/Node/node_globle/lld-link.exe"
      - "E:/Node/node_globle/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
      - "E:/opencv/build/x64/vc16/bin/lld-link.com"
      - "E:/opencv/build/x64/vc16/bin/lld-link.exe"
      - "E:/opencv/build/x64/vc16/bin/lld-link"
      - "E:/cursor/resources/app/bin/lld-link.com"
      - "E:/cursor/resources/app/bin/lld-link.exe"
      - "E:/cursor/resources/app/bin/lld-link"
      - "E:/mingw64/bin/lld-link.com"
      - "E:/mingw64/bin/lld-link.exe"
      - "E:/mingw64/bin/lld-link"
      - "E:/cmake-4.1.1-windows-x86_64/bin/lld-link.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/lld-link.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/lld-link"
      - "E:/opencv/mingw_build/bin/lld-link.com"
      - "E:/opencv/mingw_build/bin/lld-link.exe"
      - "E:/opencv/mingw_build/bin/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/mingw64/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.com"
    found: "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MT"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mt"
    candidate_directories:
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/mingw64/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt.com"
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt.exe"
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/mt.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/mt.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/mt"
      - "C:/Windows/System32/mt.com"
      - "C:/Windows/System32/mt.exe"
      - "C:/Windows/System32/mt"
      - "C:/Windows/mt.com"
      - "C:/Windows/mt.exe"
      - "C:/Windows/mt"
      - "C:/Windows/System32/wbem/mt.com"
      - "C:/Windows/System32/wbem/mt.exe"
      - "C:/Windows/System32/wbem/mt"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt"
      - "C:/Windows/System32/OpenSSH/mt.com"
      - "C:/Windows/System32/OpenSSH/mt.exe"
      - "C:/Windows/System32/OpenSSH/mt"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mt.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mt.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mt"
      - "D:/Git/cmd/mt.com"
      - "D:/Git/cmd/mt.exe"
      - "D:/Git/cmd/mt"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/mt.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/mt.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/mt"
      - "C:/Program Files/dotnet/mt.com"
      - "C:/Program Files/dotnet/mt.exe"
      - "C:/Program Files/dotnet/mt"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/mt.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/mt.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/mt"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/mt.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/mt.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/mt"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/mt.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/mt.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/mt"
      - "E:/Node/mt.com"
      - "E:/Node/mt.exe"
      - "E:/Node/mt"
      - "C:/Program Files/CMake/bin/mt.com"
      - "C:/Program Files/CMake/bin/mt.exe"
      - "C:/Program Files/CMake/bin/mt"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/mt.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/mt.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/mt"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mt"
      - "E:/Node/node_globle/mt.com"
      - "E:/Node/node_globle/mt.exe"
      - "E:/Node/node_globle/mt"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/mt"
      - "E:/opencv/build/x64/vc16/bin/mt.com"
      - "E:/opencv/build/x64/vc16/bin/mt.exe"
      - "E:/opencv/build/x64/vc16/bin/mt"
      - "E:/cursor/resources/app/bin/mt.com"
      - "E:/cursor/resources/app/bin/mt.exe"
      - "E:/cursor/resources/app/bin/mt"
      - "E:/mingw64/bin/mt.com"
      - "E:/mingw64/bin/mt.exe"
      - "E:/mingw64/bin/mt"
      - "E:/cmake-4.1.1-windows-x86_64/bin/mt.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/mt.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/mt"
      - "E:/opencv/mingw_build/bin/mt.com"
      - "E:/opencv/mingw_build/bin/mt.exe"
      - "E:/opencv/mingw_build/bin/mt"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lib"
    candidate_directories:
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/mingw64/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.com"
    found: "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.19+164abd434
      鐢熸垚鍚姩鏃堕棿涓?2025/9/5 19:13:31銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\4.1.0\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        E:\\VS\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        E:\\VS\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\4.1.0\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=E:\\VS\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淒:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\4.1.0\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.41
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/4.1.0/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/mingw64/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.com"
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.exe"
      - "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/lld-link.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/lld-link.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link"
      - "D:/Git/cmd/lld-link.com"
      - "D:/Git/cmd/lld-link.exe"
      - "D:/Git/cmd/lld-link"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/lld-link.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/lld-link.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/lld-link.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/lld-link.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/lld-link"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/lld-link.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/lld-link.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/lld-link"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/lld-link.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/lld-link.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/lld-link"
      - "E:/Node/lld-link.com"
      - "E:/Node/lld-link.exe"
      - "E:/Node/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/lld-link"
      - "E:/Node/node_globle/lld-link.com"
      - "E:/Node/node_globle/lld-link.exe"
      - "E:/Node/node_globle/lld-link"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/lld-link"
      - "E:/opencv/build/x64/vc16/bin/lld-link.com"
      - "E:/opencv/build/x64/vc16/bin/lld-link.exe"
      - "E:/opencv/build/x64/vc16/bin/lld-link"
      - "E:/cursor/resources/app/bin/lld-link.com"
      - "E:/cursor/resources/app/bin/lld-link.exe"
      - "E:/cursor/resources/app/bin/lld-link"
      - "E:/mingw64/bin/lld-link.com"
      - "E:/mingw64/bin/lld-link.exe"
      - "E:/mingw64/bin/lld-link"
      - "E:/cmake-4.1.1-windows-x86_64/bin/lld-link.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/lld-link.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/lld-link"
      - "E:/opencv/mingw_build/bin/lld-link.com"
      - "E:/opencv/mingw_build/bin/lld-link.exe"
      - "E:/opencv/mingw_build/bin/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:574 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:547 (__windows_compiler_msvc_enable_rc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC-C.cmake:5 (__windows_compiler_msvc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCInformation.cmake:48 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/mingw64/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/Rasterizer/bin/"
      - "C:/Program Files (x86)/Rasterizer/sbin/"
      - "C:/Program Files (x86)/Rasterizer/"
    searched_directories:
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/rc.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/rc.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/rc"
      - "C:/Windows/System32/rc.com"
      - "C:/Windows/System32/rc.exe"
      - "C:/Windows/System32/rc"
      - "C:/Windows/rc.com"
      - "C:/Windows/rc.exe"
      - "C:/Windows/rc"
      - "C:/Windows/System32/wbem/rc.com"
      - "C:/Windows/System32/wbem/rc.exe"
      - "C:/Windows/System32/wbem/rc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc"
      - "C:/Windows/System32/OpenSSH/rc.com"
      - "C:/Windows/System32/OpenSSH/rc.exe"
      - "C:/Windows/System32/OpenSSH/rc"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/rc.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/rc.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/rc"
      - "D:/Git/cmd/rc.com"
      - "D:/Git/cmd/rc.exe"
      - "D:/Git/cmd/rc"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/rc.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/rc.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/rc"
      - "C:/Program Files/dotnet/rc.com"
      - "C:/Program Files/dotnet/rc.exe"
      - "C:/Program Files/dotnet/rc"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/rc.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/rc.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/rc"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/rc.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/rc.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/rc"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/rc.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/rc.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/rc"
      - "E:/Node/rc.com"
      - "E:/Node/rc.exe"
      - "E:/Node/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/rc.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/rc.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/rc"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/rc"
      - "E:/Node/node_globle/rc.com"
      - "E:/Node/node_globle/rc.exe"
      - "E:/Node/node_globle/rc"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/rc"
      - "E:/opencv/build/x64/vc16/bin/rc.com"
      - "E:/opencv/build/x64/vc16/bin/rc.exe"
      - "E:/opencv/build/x64/vc16/bin/rc"
      - "E:/cursor/resources/app/bin/rc.com"
      - "E:/cursor/resources/app/bin/rc.exe"
      - "E:/cursor/resources/app/bin/rc"
      - "E:/mingw64/bin/rc.com"
      - "E:/mingw64/bin/rc.exe"
      - "E:/mingw64/bin/rc"
      - "E:/cmake-4.1.1-windows-x86_64/bin/rc.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/rc.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/rc"
      - "E:/opencv/mingw_build/bin/rc.com"
      - "E:/opencv/mingw_build/bin/rc.exe"
      - "E:/opencv/mingw_build/bin/rc"
      - "C:/Program Files/bin/rc.com"
      - "C:/Program Files/bin/rc.exe"
      - "C:/Program Files/bin/rc"
      - "C:/Program Files/sbin/rc.com"
      - "C:/Program Files/sbin/rc.exe"
      - "C:/Program Files/sbin/rc"
      - "C:/Program Files/rc.com"
      - "C:/Program Files/rc.exe"
      - "C:/Program Files/rc"
      - "C:/Program Files (x86)/bin/rc.com"
      - "C:/Program Files (x86)/bin/rc.exe"
      - "C:/Program Files (x86)/bin/rc"
      - "C:/Program Files (x86)/sbin/rc.com"
      - "C:/Program Files (x86)/sbin/rc.exe"
      - "C:/Program Files (x86)/sbin/rc"
      - "C:/Program Files (x86)/rc.com"
      - "C:/Program Files (x86)/rc.exe"
      - "C:/Program Files (x86)/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Program Files/CMake/sbin/rc.com"
      - "C:/Program Files/CMake/sbin/rc.exe"
      - "C:/Program Files/CMake/sbin/rc"
      - "C:/Program Files/CMake/rc.com"
      - "C:/Program Files/CMake/rc.exe"
      - "C:/Program Files/CMake/rc"
      - "C:/Program Files (x86)/Rasterizer/bin/rc.com"
      - "C:/Program Files (x86)/Rasterizer/bin/rc.exe"
      - "C:/Program Files (x86)/Rasterizer/bin/rc"
      - "C:/Program Files (x86)/Rasterizer/sbin/rc.com"
      - "C:/Program Files (x86)/Rasterizer/sbin/rc.exe"
      - "C:/Program Files (x86)/Rasterizer/sbin/rc"
      - "C:/Program Files (x86)/Rasterizer/rc.com"
      - "C:/Program Files (x86)/Rasterizer/rc.exe"
      - "C:/Program Files (x86)/Rasterizer/rc"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/Rasterizer"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/Rasterizer"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-b01bl5"
      binary: "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-b01bl5"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-b01bl5'
        
        Run Build Command(s): E:/VS/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_789e7.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.19+164abd434
        鐢熸垚鍚姩鏃堕棿涓?2025/9/5 19:13:31銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\CMakeScratch\\TryCompile-b01bl5\\cmTC_789e7.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_789e7.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\CMakeScratch\\TryCompile-b01bl5\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_789e7.dir\\Debug\\cmTC_789e7.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_789e7.dir\\Debug\\cmTC_789e7.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_789e7.dir\\Debug\\cmTC_789e7.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          E:\\VS\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_789e7.dir\\Debug\\\\" /Fd"cmTC_789e7.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35215 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_789e7.dir\\Debug\\\\" /Fd"cmTC_789e7.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          E:\\VS\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\CMakeScratch\\TryCompile-b01bl5\\Debug\\cmTC_789e7.exe" /INCREMENTAL /ILK:"cmTC_789e7.dir\\Debug\\cmTC_789e7.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/pet/CODE/鍥惧舰/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-b01bl5/Debug/cmTC_789e7.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/pet/CODE/鍥惧舰/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-b01bl5/Debug/cmTC_789e7.lib" /MACHINE:X64  /machine:x64 cmTC_789e7.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_789e7.vcxproj -> D:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\CMakeScratch\\TryCompile-b01bl5\\Debug\\cmTC_789e7.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_789e7.dir\\Debug\\cmTC_789e7.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_789e7.dir\\Debug\\cmTC_789e7.tlog\\cmTC_789e7.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\CMakeScratch\\TryCompile-b01bl5\\cmTC_789e7.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.40
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35215.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-oylpd8"
      binary: "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-oylpd8"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-oylpd8'
        
        Run Build Command(s): E:/VS/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_c36a7.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.19+164abd434
        鐢熸垚鍚姩鏃堕棿涓?2025/9/5 19:13:32銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oylpd8\\cmTC_c36a7.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_c36a7.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oylpd8\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_c36a7.dir\\Debug\\cmTC_c36a7.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_c36a7.dir\\Debug\\cmTC_c36a7.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_c36a7.dir\\Debug\\cmTC_c36a7.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          E:\\VS\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_c36a7.dir\\Debug\\\\" /Fd"cmTC_c36a7.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35215 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_c36a7.dir\\Debug\\\\" /Fd"cmTC_c36a7.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          E:\\VS\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oylpd8\\Debug\\cmTC_c36a7.exe" /INCREMENTAL /ILK:"cmTC_c36a7.dir\\Debug\\cmTC_c36a7.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/pet/CODE/鍥惧舰/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-oylpd8/Debug/cmTC_c36a7.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/pet/CODE/鍥惧舰/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-oylpd8/Debug/cmTC_c36a7.lib" /MACHINE:X64  /machine:x64 cmTC_c36a7.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_c36a7.vcxproj -> D:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oylpd8\\Debug\\cmTC_c36a7.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_c36a7.dir\\Debug\\cmTC_c36a7.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_c36a7.dir\\Debug\\cmTC_c36a7.tlog\\cmTC_c36a7.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\pet\\CODE\\鍥惧舰\\HHMM\\Hw3\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oylpd8\\cmTC_c36a7.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.40
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "E:/VS/2022/Enterprise/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35215.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
