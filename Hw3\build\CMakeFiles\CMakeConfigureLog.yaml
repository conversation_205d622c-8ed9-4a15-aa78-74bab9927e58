
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeMinGWFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mingw32-make.exe"
    candidate_directories:
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/mingw64/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
      - "/REGISTRY-NOTFOUND/bin/"
      - "c:/MinGW/bin/"
      - "/MinGW/bin/"
      - "/REGISTRY-NOTFOUND/MinGW/bin/"
    searched_directories:
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/mingw32-make.exe.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/mingw32-make.exe"
      - "C:/Windows/System32/mingw32-make.exe.com"
      - "C:/Windows/System32/mingw32-make.exe"
      - "C:/Windows/mingw32-make.exe.com"
      - "C:/Windows/mingw32-make.exe"
      - "C:/Windows/System32/wbem/mingw32-make.exe.com"
      - "C:/Windows/System32/wbem/mingw32-make.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mingw32-make.exe.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mingw32-make.exe"
      - "C:/Windows/System32/OpenSSH/mingw32-make.exe.com"
      - "C:/Windows/System32/OpenSSH/mingw32-make.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mingw32-make.exe.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mingw32-make.exe"
      - "D:/Git/cmd/mingw32-make.exe.com"
      - "D:/Git/cmd/mingw32-make.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/mingw32-make.exe.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/mingw32-make.exe"
      - "C:/Program Files/dotnet/mingw32-make.exe.com"
      - "C:/Program Files/dotnet/mingw32-make.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/mingw32-make.exe.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/mingw32-make.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/mingw32-make.exe.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/mingw32-make.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/mingw32-make.exe.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/mingw32-make.exe"
      - "E:/Node/mingw32-make.exe.com"
      - "E:/Node/mingw32-make.exe"
      - "C:/Program Files/CMake/bin/mingw32-make.exe.com"
      - "C:/Program Files/CMake/bin/mingw32-make.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/mingw32-make.exe.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/mingw32-make.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mingw32-make.exe.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mingw32-make.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mingw32-make.exe.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mingw32-make.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mingw32-make.exe.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/mingw32-make.exe"
      - "E:/Node/node_globle/mingw32-make.exe.com"
      - "E:/Node/node_globle/mingw32-make.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/mingw32-make.exe.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/mingw32-make.exe"
      - "E:/opencv/build/x64/vc16/bin/mingw32-make.exe.com"
      - "E:/opencv/build/x64/vc16/bin/mingw32-make.exe"
      - "E:/cursor/resources/app/bin/mingw32-make.exe.com"
      - "E:/cursor/resources/app/bin/mingw32-make.exe"
      - "E:/mingw64/bin/mingw32-make.exe.com"
    found: "E:/mingw64/bin/mingw32-make.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake:63 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:64 (_cmake_find_compiler)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER"
    description: "C compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: false
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "cc"
      - "gcc"
      - "cl"
      - "bcc"
      - "xlc"
      - "icx"
      - "clang"
    candidate_directories:
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/mingw64/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/cc.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/cc.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/cc"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/cl.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/cl.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/cl"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/bcc.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/bcc.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/bcc"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/xlc.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/xlc.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/xlc"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/icx.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/icx.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/icx"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/clang.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/clang.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/clang"
      - "C:/Windows/System32/cc.com"
      - "C:/Windows/System32/cc.exe"
      - "C:/Windows/System32/cc"
      - "C:/Windows/System32/gcc.com"
      - "C:/Windows/System32/gcc.exe"
      - "C:/Windows/System32/gcc"
      - "C:/Windows/System32/cl.com"
      - "C:/Windows/System32/cl.exe"
      - "C:/Windows/System32/cl"
      - "C:/Windows/System32/bcc.com"
      - "C:/Windows/System32/bcc.exe"
      - "C:/Windows/System32/bcc"
      - "C:/Windows/System32/xlc.com"
      - "C:/Windows/System32/xlc.exe"
      - "C:/Windows/System32/xlc"
      - "C:/Windows/System32/icx.com"
      - "C:/Windows/System32/icx.exe"
      - "C:/Windows/System32/icx"
      - "C:/Windows/System32/clang.com"
      - "C:/Windows/System32/clang.exe"
      - "C:/Windows/System32/clang"
      - "C:/Windows/cc.com"
      - "C:/Windows/cc.exe"
      - "C:/Windows/cc"
      - "C:/Windows/gcc.com"
      - "C:/Windows/gcc.exe"
      - "C:/Windows/gcc"
      - "C:/Windows/cl.com"
      - "C:/Windows/cl.exe"
      - "C:/Windows/cl"
      - "C:/Windows/bcc.com"
      - "C:/Windows/bcc.exe"
      - "C:/Windows/bcc"
      - "C:/Windows/xlc.com"
      - "C:/Windows/xlc.exe"
      - "C:/Windows/xlc"
      - "C:/Windows/icx.com"
      - "C:/Windows/icx.exe"
      - "C:/Windows/icx"
      - "C:/Windows/clang.com"
      - "C:/Windows/clang.exe"
      - "C:/Windows/clang"
      - "C:/Windows/System32/wbem/cc.com"
      - "C:/Windows/System32/wbem/cc.exe"
      - "C:/Windows/System32/wbem/cc"
      - "C:/Windows/System32/wbem/gcc.com"
      - "C:/Windows/System32/wbem/gcc.exe"
      - "C:/Windows/System32/wbem/gcc"
      - "C:/Windows/System32/wbem/cl.com"
      - "C:/Windows/System32/wbem/cl.exe"
      - "C:/Windows/System32/wbem/cl"
      - "C:/Windows/System32/wbem/bcc.com"
      - "C:/Windows/System32/wbem/bcc.exe"
      - "C:/Windows/System32/wbem/bcc"
      - "C:/Windows/System32/wbem/xlc.com"
      - "C:/Windows/System32/wbem/xlc.exe"
      - "C:/Windows/System32/wbem/xlc"
      - "C:/Windows/System32/wbem/icx.com"
      - "C:/Windows/System32/wbem/icx.exe"
      - "C:/Windows/System32/wbem/icx"
      - "C:/Windows/System32/wbem/clang.com"
      - "C:/Windows/System32/wbem/clang.exe"
      - "C:/Windows/System32/wbem/clang"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cl.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cl.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/cl"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/bcc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/bcc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/bcc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/xlc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/xlc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/xlc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/icx.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/icx.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/icx"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/clang.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/clang.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/clang"
      - "C:/Windows/System32/OpenSSH/cc.com"
      - "C:/Windows/System32/OpenSSH/cc.exe"
      - "C:/Windows/System32/OpenSSH/cc"
      - "C:/Windows/System32/OpenSSH/gcc.com"
      - "C:/Windows/System32/OpenSSH/gcc.exe"
      - "C:/Windows/System32/OpenSSH/gcc"
      - "C:/Windows/System32/OpenSSH/cl.com"
      - "C:/Windows/System32/OpenSSH/cl.exe"
      - "C:/Windows/System32/OpenSSH/cl"
      - "C:/Windows/System32/OpenSSH/bcc.com"
      - "C:/Windows/System32/OpenSSH/bcc.exe"
      - "C:/Windows/System32/OpenSSH/bcc"
      - "C:/Windows/System32/OpenSSH/xlc.com"
      - "C:/Windows/System32/OpenSSH/xlc.exe"
      - "C:/Windows/System32/OpenSSH/xlc"
      - "C:/Windows/System32/OpenSSH/icx.com"
      - "C:/Windows/System32/OpenSSH/icx.exe"
      - "C:/Windows/System32/OpenSSH/icx"
      - "C:/Windows/System32/OpenSSH/clang.com"
      - "C:/Windows/System32/OpenSSH/clang.exe"
      - "C:/Windows/System32/OpenSSH/clang"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/cc.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/cc.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/cc"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/cl.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/cl.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/cl"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/bcc.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/bcc.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/bcc"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/xlc.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/xlc.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/xlc"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/icx.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/icx.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/icx"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/clang.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/clang.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/clang"
      - "D:/Git/cmd/cc.com"
      - "D:/Git/cmd/cc.exe"
      - "D:/Git/cmd/cc"
      - "D:/Git/cmd/gcc.com"
      - "D:/Git/cmd/gcc.exe"
      - "D:/Git/cmd/gcc"
      - "D:/Git/cmd/cl.com"
      - "D:/Git/cmd/cl.exe"
      - "D:/Git/cmd/cl"
      - "D:/Git/cmd/bcc.com"
      - "D:/Git/cmd/bcc.exe"
      - "D:/Git/cmd/bcc"
      - "D:/Git/cmd/xlc.com"
      - "D:/Git/cmd/xlc.exe"
      - "D:/Git/cmd/xlc"
      - "D:/Git/cmd/icx.com"
      - "D:/Git/cmd/icx.exe"
      - "D:/Git/cmd/icx"
      - "D:/Git/cmd/clang.com"
      - "D:/Git/cmd/clang.exe"
      - "D:/Git/cmd/clang"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/cc.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/cc.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/cc"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/cl.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/cl.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/cl"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/bcc.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/bcc.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/bcc"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/xlc.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/xlc.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/xlc"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/icx.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/icx.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/icx"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/clang.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/clang.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/clang"
      - "C:/Program Files/dotnet/cc.com"
      - "C:/Program Files/dotnet/cc.exe"
      - "C:/Program Files/dotnet/cc"
      - "C:/Program Files/dotnet/gcc.com"
      - "C:/Program Files/dotnet/gcc.exe"
      - "C:/Program Files/dotnet/gcc"
      - "C:/Program Files/dotnet/cl.com"
      - "C:/Program Files/dotnet/cl.exe"
      - "C:/Program Files/dotnet/cl"
      - "C:/Program Files/dotnet/bcc.com"
      - "C:/Program Files/dotnet/bcc.exe"
      - "C:/Program Files/dotnet/bcc"
      - "C:/Program Files/dotnet/xlc.com"
      - "C:/Program Files/dotnet/xlc.exe"
      - "C:/Program Files/dotnet/xlc"
      - "C:/Program Files/dotnet/icx.com"
      - "C:/Program Files/dotnet/icx.exe"
      - "C:/Program Files/dotnet/icx"
      - "C:/Program Files/dotnet/clang.com"
      - "C:/Program Files/dotnet/clang.exe"
      - "C:/Program Files/dotnet/clang"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/cc.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/cc.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/cc"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/cl.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/cl.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/cl"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/bcc.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/bcc.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/bcc"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/xlc.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/xlc.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/xlc"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/icx.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/icx.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/icx"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/clang.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/clang.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/clang"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/cc.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/cc.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/cc"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/cl.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/cl.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/cl"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/bcc.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/bcc.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/bcc"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/xlc.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/xlc.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/xlc"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/icx.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/icx.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/icx"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/clang.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/clang.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/clang"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/cc.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/cc.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/cc"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/cl.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/cl.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/cl"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/bcc.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/bcc.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/bcc"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/xlc.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/xlc.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/xlc"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/icx.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/icx.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/icx"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/clang.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/clang.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/clang"
      - "E:/Node/cc.com"
      - "E:/Node/cc.exe"
      - "E:/Node/cc"
      - "E:/Node/gcc.com"
      - "E:/Node/gcc.exe"
      - "E:/Node/gcc"
      - "E:/Node/cl.com"
      - "E:/Node/cl.exe"
      - "E:/Node/cl"
      - "E:/Node/bcc.com"
      - "E:/Node/bcc.exe"
      - "E:/Node/bcc"
      - "E:/Node/xlc.com"
      - "E:/Node/xlc.exe"
      - "E:/Node/xlc"
      - "E:/Node/icx.com"
      - "E:/Node/icx.exe"
      - "E:/Node/icx"
      - "E:/Node/clang.com"
      - "E:/Node/clang.exe"
      - "E:/Node/clang"
      - "C:/Program Files/CMake/bin/cc.com"
      - "C:/Program Files/CMake/bin/cc.exe"
      - "C:/Program Files/CMake/bin/cc"
      - "C:/Program Files/CMake/bin/gcc.com"
      - "C:/Program Files/CMake/bin/gcc.exe"
      - "C:/Program Files/CMake/bin/gcc"
      - "C:/Program Files/CMake/bin/cl.com"
      - "C:/Program Files/CMake/bin/cl.exe"
      - "C:/Program Files/CMake/bin/cl"
      - "C:/Program Files/CMake/bin/bcc.com"
      - "C:/Program Files/CMake/bin/bcc.exe"
      - "C:/Program Files/CMake/bin/bcc"
      - "C:/Program Files/CMake/bin/xlc.com"
      - "C:/Program Files/CMake/bin/xlc.exe"
      - "C:/Program Files/CMake/bin/xlc"
      - "C:/Program Files/CMake/bin/icx.com"
      - "C:/Program Files/CMake/bin/icx.exe"
      - "C:/Program Files/CMake/bin/icx"
      - "C:/Program Files/CMake/bin/clang.com"
      - "C:/Program Files/CMake/bin/clang.exe"
      - "C:/Program Files/CMake/bin/clang"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/cc.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/cc.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/cc"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/cl.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/cl.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/cl"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/bcc.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/bcc.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/bcc"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/xlc.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/xlc.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/xlc"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/icx.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/icx.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/icx"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/clang.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/clang.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/clang"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/cc.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/cc.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/cc"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/cl.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/cl.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/cl"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/bcc.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/bcc.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/bcc"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/xlc.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/xlc.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/xlc"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/icx.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/icx.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/icx"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/clang.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/clang.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/clang"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cl.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cl.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/cl"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/bcc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/bcc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/bcc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/xlc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/xlc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/xlc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/icx.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/icx.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/icx"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/clang.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/clang.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/clang"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/cc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/cc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/cc"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/cl.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/cl.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/cl"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/bcc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/bcc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/bcc"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/xlc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/xlc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/xlc"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/icx.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/icx.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/icx"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/clang.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/clang.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/clang"
      - "E:/Node/node_globle/cc.com"
      - "E:/Node/node_globle/cc.exe"
      - "E:/Node/node_globle/cc"
      - "E:/Node/node_globle/gcc.com"
      - "E:/Node/node_globle/gcc.exe"
      - "E:/Node/node_globle/gcc"
      - "E:/Node/node_globle/cl.com"
      - "E:/Node/node_globle/cl.exe"
      - "E:/Node/node_globle/cl"
      - "E:/Node/node_globle/bcc.com"
      - "E:/Node/node_globle/bcc.exe"
      - "E:/Node/node_globle/bcc"
      - "E:/Node/node_globle/xlc.com"
      - "E:/Node/node_globle/xlc.exe"
      - "E:/Node/node_globle/xlc"
      - "E:/Node/node_globle/icx.com"
      - "E:/Node/node_globle/icx.exe"
      - "E:/Node/node_globle/icx"
      - "E:/Node/node_globle/clang.com"
      - "E:/Node/node_globle/clang.exe"
      - "E:/Node/node_globle/clang"
      - "C:/Users/<USER>/AppData/Roaming/npm/cc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/cc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/cc"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc"
      - "C:/Users/<USER>/AppData/Roaming/npm/cl.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/cl.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/cl"
      - "C:/Users/<USER>/AppData/Roaming/npm/bcc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/bcc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/bcc"
      - "C:/Users/<USER>/AppData/Roaming/npm/xlc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/xlc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/xlc"
      - "C:/Users/<USER>/AppData/Roaming/npm/icx.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/icx.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/icx"
      - "C:/Users/<USER>/AppData/Roaming/npm/clang.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/clang.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/clang"
      - "E:/opencv/build/x64/vc16/bin/cc.com"
      - "E:/opencv/build/x64/vc16/bin/cc.exe"
      - "E:/opencv/build/x64/vc16/bin/cc"
      - "E:/opencv/build/x64/vc16/bin/gcc.com"
      - "E:/opencv/build/x64/vc16/bin/gcc.exe"
      - "E:/opencv/build/x64/vc16/bin/gcc"
      - "E:/opencv/build/x64/vc16/bin/cl.com"
      - "E:/opencv/build/x64/vc16/bin/cl.exe"
      - "E:/opencv/build/x64/vc16/bin/cl"
      - "E:/opencv/build/x64/vc16/bin/bcc.com"
      - "E:/opencv/build/x64/vc16/bin/bcc.exe"
      - "E:/opencv/build/x64/vc16/bin/bcc"
      - "E:/opencv/build/x64/vc16/bin/xlc.com"
      - "E:/opencv/build/x64/vc16/bin/xlc.exe"
      - "E:/opencv/build/x64/vc16/bin/xlc"
      - "E:/opencv/build/x64/vc16/bin/icx.com"
      - "E:/opencv/build/x64/vc16/bin/icx.exe"
      - "E:/opencv/build/x64/vc16/bin/icx"
      - "E:/opencv/build/x64/vc16/bin/clang.com"
      - "E:/opencv/build/x64/vc16/bin/clang.exe"
      - "E:/opencv/build/x64/vc16/bin/clang"
      - "E:/cursor/resources/app/bin/cc.com"
      - "E:/cursor/resources/app/bin/cc.exe"
      - "E:/cursor/resources/app/bin/cc"
      - "E:/cursor/resources/app/bin/gcc.com"
      - "E:/cursor/resources/app/bin/gcc.exe"
      - "E:/cursor/resources/app/bin/gcc"
      - "E:/cursor/resources/app/bin/cl.com"
      - "E:/cursor/resources/app/bin/cl.exe"
      - "E:/cursor/resources/app/bin/cl"
      - "E:/cursor/resources/app/bin/bcc.com"
      - "E:/cursor/resources/app/bin/bcc.exe"
      - "E:/cursor/resources/app/bin/bcc"
      - "E:/cursor/resources/app/bin/xlc.com"
      - "E:/cursor/resources/app/bin/xlc.exe"
      - "E:/cursor/resources/app/bin/xlc"
      - "E:/cursor/resources/app/bin/icx.com"
      - "E:/cursor/resources/app/bin/icx.exe"
      - "E:/cursor/resources/app/bin/icx"
      - "E:/cursor/resources/app/bin/clang.com"
      - "E:/cursor/resources/app/bin/clang.exe"
      - "E:/cursor/resources/app/bin/clang"
      - "E:/mingw64/bin/cc.com"
    found: "E:/mingw64/bin/cc.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: E:/mingw64/bin/cc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/4.1.0/CompilerIdC/a.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ar"
    candidate_directories:
      - "E:/mingw64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/mingw64/bin/ar.com"
    found: "E:/mingw64/bin/ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ranlib"
    candidate_directories:
      - "E:/mingw64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/mingw64/bin/ranlib.com"
    found: "E:/mingw64/bin/ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "strip"
    candidate_directories:
      - "E:/mingw64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/mingw64/bin/strip.com"
    found: "E:/mingw64/bin/strip.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld"
    candidate_directories:
      - "E:/mingw64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/mingw64/bin/ld.com"
    found: "E:/mingw64/bin/ld.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nm"
    candidate_directories:
      - "E:/mingw64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/mingw64/bin/nm.com"
    found: "E:/mingw64/bin/nm.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objdump"
    candidate_directories:
      - "E:/mingw64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/mingw64/bin/objdump.com"
    found: "E:/mingw64/bin/objdump.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objcopy"
    candidate_directories:
      - "E:/mingw64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/mingw64/bin/objcopy.com"
    found: "E:/mingw64/bin/objcopy.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "readelf"
    candidate_directories:
      - "E:/mingw64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/mingw64/bin/readelf.com"
    found: "E:/mingw64/bin/readelf.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dlltool"
    candidate_directories:
      - "E:/mingw64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/mingw64/bin/dlltool.com"
    found: "E:/mingw64/bin/dlltool.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "addr2line"
    candidate_directories:
      - "E:/mingw64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/mingw64/bin/addr2line.com"
    found: "E:/mingw64/bin/addr2line.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "E:/mingw64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/mingw64/bin/tapi.com"
      - "E:/mingw64/bin/tapi.exe"
      - "E:/mingw64/bin/tapi"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/tapi.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/tapi.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/tapi"
      - "C:/Windows/System32/tapi.com"
      - "C:/Windows/System32/tapi.exe"
      - "C:/Windows/System32/tapi"
      - "C:/Windows/tapi.com"
      - "C:/Windows/tapi.exe"
      - "C:/Windows/tapi"
      - "C:/Windows/System32/wbem/tapi.com"
      - "C:/Windows/System32/wbem/tapi.exe"
      - "C:/Windows/System32/wbem/tapi"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi"
      - "C:/Windows/System32/OpenSSH/tapi.com"
      - "C:/Windows/System32/OpenSSH/tapi.exe"
      - "C:/Windows/System32/OpenSSH/tapi"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/tapi.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/tapi.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/tapi"
      - "D:/Git/cmd/tapi.com"
      - "D:/Git/cmd/tapi.exe"
      - "D:/Git/cmd/tapi"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/tapi.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/tapi.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/tapi"
      - "C:/Program Files/dotnet/tapi.com"
      - "C:/Program Files/dotnet/tapi.exe"
      - "C:/Program Files/dotnet/tapi"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/tapi.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/tapi.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/tapi"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/tapi.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/tapi.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/tapi"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/tapi.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/tapi.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/tapi"
      - "E:/Node/tapi.com"
      - "E:/Node/tapi.exe"
      - "E:/Node/tapi"
      - "C:/Program Files/CMake/bin/tapi.com"
      - "C:/Program Files/CMake/bin/tapi.exe"
      - "C:/Program Files/CMake/bin/tapi"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/tapi.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/tapi.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/tapi"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/tapi.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/tapi.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/tapi"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi"
      - "E:/Node/node_globle/tapi.com"
      - "E:/Node/node_globle/tapi.exe"
      - "E:/Node/node_globle/tapi"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi"
      - "E:/opencv/build/x64/vc16/bin/tapi.com"
      - "E:/opencv/build/x64/vc16/bin/tapi.exe"
      - "E:/opencv/build/x64/vc16/bin/tapi"
      - "E:/cursor/resources/app/bin/tapi.com"
      - "E:/cursor/resources/app/bin/tapi.exe"
      - "E:/cursor/resources/app/bin/tapi"
      - "E:/cmake-4.1.1-windows-x86_64/bin/tapi.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/tapi.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/tapi"
      - "E:/opencv/mingw_build/bin/tapi.com"
      - "E:/opencv/mingw_build/bin/tapi.exe"
      - "E:/opencv/mingw_build/bin/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-15.2"
      - "gcc-ar-15"
      - "gcc-ar15"
      - "gcc-ar"
    candidate_directories:
      - "E:/mingw64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/mingw64/bin/gcc-ar-15.2.com"
      - "E:/mingw64/bin/gcc-ar-15.2.exe"
      - "E:/mingw64/bin/gcc-ar-15.2"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-15.2.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-15.2.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-15.2"
      - "C:/Windows/System32/gcc-ar-15.2.com"
      - "C:/Windows/System32/gcc-ar-15.2.exe"
      - "C:/Windows/System32/gcc-ar-15.2"
      - "C:/Windows/gcc-ar-15.2.com"
      - "C:/Windows/gcc-ar-15.2.exe"
      - "C:/Windows/gcc-ar-15.2"
      - "C:/Windows/System32/wbem/gcc-ar-15.2.com"
      - "C:/Windows/System32/wbem/gcc-ar-15.2.exe"
      - "C:/Windows/System32/wbem/gcc-ar-15.2"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.2.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.2.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.2"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.2.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.2.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.2"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-15.2.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-15.2.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-15.2"
      - "D:/Git/cmd/gcc-ar-15.2.com"
      - "D:/Git/cmd/gcc-ar-15.2.exe"
      - "D:/Git/cmd/gcc-ar-15.2"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-15.2.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-15.2.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-15.2"
      - "C:/Program Files/dotnet/gcc-ar-15.2.com"
      - "C:/Program Files/dotnet/gcc-ar-15.2.exe"
      - "C:/Program Files/dotnet/gcc-ar-15.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar-15.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar-15.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar-15.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar-15.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar-15.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar-15.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar-15.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar-15.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar-15.2"
      - "E:/Node/gcc-ar-15.2.com"
      - "E:/Node/gcc-ar-15.2.exe"
      - "E:/Node/gcc-ar-15.2"
      - "C:/Program Files/CMake/bin/gcc-ar-15.2.com"
      - "C:/Program Files/CMake/bin/gcc-ar-15.2.exe"
      - "C:/Program Files/CMake/bin/gcc-ar-15.2"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar-15.2.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar-15.2.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar-15.2"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-15.2.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-15.2.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-15.2"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.2.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.2.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.2"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.2.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.2.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.2"
      - "E:/Node/node_globle/gcc-ar-15.2.com"
      - "E:/Node/node_globle/gcc-ar-15.2.exe"
      - "E:/Node/node_globle/gcc-ar-15.2"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-15.2.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-15.2.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-15.2"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar-15.2.com"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar-15.2.exe"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar-15.2"
      - "E:/cursor/resources/app/bin/gcc-ar-15.2.com"
      - "E:/cursor/resources/app/bin/gcc-ar-15.2.exe"
      - "E:/cursor/resources/app/bin/gcc-ar-15.2"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar-15.2.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar-15.2.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar-15.2"
      - "E:/opencv/mingw_build/bin/gcc-ar-15.2.com"
      - "E:/opencv/mingw_build/bin/gcc-ar-15.2.exe"
      - "E:/opencv/mingw_build/bin/gcc-ar-15.2"
      - "E:/mingw64/bin/gcc-ar-15.com"
      - "E:/mingw64/bin/gcc-ar-15.exe"
      - "E:/mingw64/bin/gcc-ar-15"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-15.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-15.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-15"
      - "C:/Windows/System32/gcc-ar-15.com"
      - "C:/Windows/System32/gcc-ar-15.exe"
      - "C:/Windows/System32/gcc-ar-15"
      - "C:/Windows/gcc-ar-15.com"
      - "C:/Windows/gcc-ar-15.exe"
      - "C:/Windows/gcc-ar-15"
      - "C:/Windows/System32/wbem/gcc-ar-15.com"
      - "C:/Windows/System32/wbem/gcc-ar-15.exe"
      - "C:/Windows/System32/wbem/gcc-ar-15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-15.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-15.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-15"
      - "D:/Git/cmd/gcc-ar-15.com"
      - "D:/Git/cmd/gcc-ar-15.exe"
      - "D:/Git/cmd/gcc-ar-15"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-15.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-15.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-15"
      - "C:/Program Files/dotnet/gcc-ar-15.com"
      - "C:/Program Files/dotnet/gcc-ar-15.exe"
      - "C:/Program Files/dotnet/gcc-ar-15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar-15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar-15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar-15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar-15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar-15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar-15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar-15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar-15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar-15"
      - "E:/Node/gcc-ar-15.com"
      - "E:/Node/gcc-ar-15.exe"
      - "E:/Node/gcc-ar-15"
      - "C:/Program Files/CMake/bin/gcc-ar-15.com"
      - "C:/Program Files/CMake/bin/gcc-ar-15.exe"
      - "C:/Program Files/CMake/bin/gcc-ar-15"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar-15.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar-15.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar-15"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-15.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-15.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15"
      - "E:/Node/node_globle/gcc-ar-15.com"
      - "E:/Node/node_globle/gcc-ar-15.exe"
      - "E:/Node/node_globle/gcc-ar-15"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-15.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-15.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-15"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar-15.com"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar-15.exe"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar-15"
      - "E:/cursor/resources/app/bin/gcc-ar-15.com"
      - "E:/cursor/resources/app/bin/gcc-ar-15.exe"
      - "E:/cursor/resources/app/bin/gcc-ar-15"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar-15.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar-15.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar-15"
      - "E:/opencv/mingw_build/bin/gcc-ar-15.com"
      - "E:/opencv/mingw_build/bin/gcc-ar-15.exe"
      - "E:/opencv/mingw_build/bin/gcc-ar-15"
      - "E:/mingw64/bin/gcc-ar15.com"
      - "E:/mingw64/bin/gcc-ar15.exe"
      - "E:/mingw64/bin/gcc-ar15"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar15.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar15.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar15"
      - "C:/Windows/System32/gcc-ar15.com"
      - "C:/Windows/System32/gcc-ar15.exe"
      - "C:/Windows/System32/gcc-ar15"
      - "C:/Windows/gcc-ar15.com"
      - "C:/Windows/gcc-ar15.exe"
      - "C:/Windows/gcc-ar15"
      - "C:/Windows/System32/wbem/gcc-ar15.com"
      - "C:/Windows/System32/wbem/gcc-ar15.exe"
      - "C:/Windows/System32/wbem/gcc-ar15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15"
      - "C:/Windows/System32/OpenSSH/gcc-ar15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar15"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar15.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar15.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar15"
      - "D:/Git/cmd/gcc-ar15.com"
      - "D:/Git/cmd/gcc-ar15.exe"
      - "D:/Git/cmd/gcc-ar15"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar15.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar15.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar15"
      - "C:/Program Files/dotnet/gcc-ar15.com"
      - "C:/Program Files/dotnet/gcc-ar15.exe"
      - "C:/Program Files/dotnet/gcc-ar15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar15"
      - "E:/Node/gcc-ar15.com"
      - "E:/Node/gcc-ar15.exe"
      - "E:/Node/gcc-ar15"
      - "C:/Program Files/CMake/bin/gcc-ar15.com"
      - "C:/Program Files/CMake/bin/gcc-ar15.exe"
      - "C:/Program Files/CMake/bin/gcc-ar15"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar15.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar15.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar15"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar15.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar15.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar15"
      - "E:/Node/node_globle/gcc-ar15.com"
      - "E:/Node/node_globle/gcc-ar15.exe"
      - "E:/Node/node_globle/gcc-ar15"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar15.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar15.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar15"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar15.com"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar15.exe"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar15"
      - "E:/cursor/resources/app/bin/gcc-ar15.com"
      - "E:/cursor/resources/app/bin/gcc-ar15.exe"
      - "E:/cursor/resources/app/bin/gcc-ar15"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar15.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar15.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar15"
      - "E:/opencv/mingw_build/bin/gcc-ar15.com"
      - "E:/opencv/mingw_build/bin/gcc-ar15.exe"
      - "E:/opencv/mingw_build/bin/gcc-ar15"
      - "E:/mingw64/bin/gcc-ar.com"
    found: "E:/mingw64/bin/gcc-ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-15.2"
      - "gcc-ranlib-15"
      - "gcc-ranlib15"
      - "gcc-ranlib"
    candidate_directories:
      - "E:/mingw64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/mingw64/bin/gcc-ranlib-15.2.com"
      - "E:/mingw64/bin/gcc-ranlib-15.2.exe"
      - "E:/mingw64/bin/gcc-ranlib-15.2"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-15.2.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-15.2.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-15.2"
      - "C:/Windows/System32/gcc-ranlib-15.2.com"
      - "C:/Windows/System32/gcc-ranlib-15.2.exe"
      - "C:/Windows/System32/gcc-ranlib-15.2"
      - "C:/Windows/gcc-ranlib-15.2.com"
      - "C:/Windows/gcc-ranlib-15.2.exe"
      - "C:/Windows/gcc-ranlib-15.2"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.2.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.2.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.2"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.2.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.2.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.2"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.2.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.2.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.2"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-15.2.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-15.2.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-15.2"
      - "D:/Git/cmd/gcc-ranlib-15.2.com"
      - "D:/Git/cmd/gcc-ranlib-15.2.exe"
      - "D:/Git/cmd/gcc-ranlib-15.2"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-15.2.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-15.2.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-15.2"
      - "C:/Program Files/dotnet/gcc-ranlib-15.2.com"
      - "C:/Program Files/dotnet/gcc-ranlib-15.2.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-15.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib-15.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib-15.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib-15.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib-15.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib-15.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib-15.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib-15.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib-15.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib-15.2"
      - "E:/Node/gcc-ranlib-15.2.com"
      - "E:/Node/gcc-ranlib-15.2.exe"
      - "E:/Node/gcc-ranlib-15.2"
      - "C:/Program Files/CMake/bin/gcc-ranlib-15.2.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib-15.2.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib-15.2"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib-15.2.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib-15.2.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib-15.2"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-15.2.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-15.2.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-15.2"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.2.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.2.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.2"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.2.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.2.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.2"
      - "E:/Node/node_globle/gcc-ranlib-15.2.com"
      - "E:/Node/node_globle/gcc-ranlib-15.2.exe"
      - "E:/Node/node_globle/gcc-ranlib-15.2"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-15.2.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-15.2.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-15.2"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib-15.2.com"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib-15.2.exe"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib-15.2"
      - "E:/cursor/resources/app/bin/gcc-ranlib-15.2.com"
      - "E:/cursor/resources/app/bin/gcc-ranlib-15.2.exe"
      - "E:/cursor/resources/app/bin/gcc-ranlib-15.2"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib-15.2.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib-15.2.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib-15.2"
      - "E:/opencv/mingw_build/bin/gcc-ranlib-15.2.com"
      - "E:/opencv/mingw_build/bin/gcc-ranlib-15.2.exe"
      - "E:/opencv/mingw_build/bin/gcc-ranlib-15.2"
      - "E:/mingw64/bin/gcc-ranlib-15.com"
      - "E:/mingw64/bin/gcc-ranlib-15.exe"
      - "E:/mingw64/bin/gcc-ranlib-15"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-15.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-15.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-15"
      - "C:/Windows/System32/gcc-ranlib-15.com"
      - "C:/Windows/System32/gcc-ranlib-15.exe"
      - "C:/Windows/System32/gcc-ranlib-15"
      - "C:/Windows/gcc-ranlib-15.com"
      - "C:/Windows/gcc-ranlib-15.exe"
      - "C:/Windows/gcc-ranlib-15"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-15.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-15.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-15"
      - "D:/Git/cmd/gcc-ranlib-15.com"
      - "D:/Git/cmd/gcc-ranlib-15.exe"
      - "D:/Git/cmd/gcc-ranlib-15"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-15.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-15.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-15"
      - "C:/Program Files/dotnet/gcc-ranlib-15.com"
      - "C:/Program Files/dotnet/gcc-ranlib-15.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib-15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib-15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib-15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib-15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib-15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib-15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib-15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib-15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib-15"
      - "E:/Node/gcc-ranlib-15.com"
      - "E:/Node/gcc-ranlib-15.exe"
      - "E:/Node/gcc-ranlib-15"
      - "C:/Program Files/CMake/bin/gcc-ranlib-15.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib-15.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib-15"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib-15.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib-15.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib-15"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-15.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-15.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15"
      - "E:/Node/node_globle/gcc-ranlib-15.com"
      - "E:/Node/node_globle/gcc-ranlib-15.exe"
      - "E:/Node/node_globle/gcc-ranlib-15"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-15.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-15.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-15"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib-15.com"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib-15.exe"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib-15"
      - "E:/cursor/resources/app/bin/gcc-ranlib-15.com"
      - "E:/cursor/resources/app/bin/gcc-ranlib-15.exe"
      - "E:/cursor/resources/app/bin/gcc-ranlib-15"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib-15.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib-15.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib-15"
      - "E:/opencv/mingw_build/bin/gcc-ranlib-15.com"
      - "E:/opencv/mingw_build/bin/gcc-ranlib-15.exe"
      - "E:/opencv/mingw_build/bin/gcc-ranlib-15"
      - "E:/mingw64/bin/gcc-ranlib15.com"
      - "E:/mingw64/bin/gcc-ranlib15.exe"
      - "E:/mingw64/bin/gcc-ranlib15"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib15.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib15.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib15"
      - "C:/Windows/System32/gcc-ranlib15.com"
      - "C:/Windows/System32/gcc-ranlib15.exe"
      - "C:/Windows/System32/gcc-ranlib15"
      - "C:/Windows/gcc-ranlib15.com"
      - "C:/Windows/gcc-ranlib15.exe"
      - "C:/Windows/gcc-ranlib15"
      - "C:/Windows/System32/wbem/gcc-ranlib15.com"
      - "C:/Windows/System32/wbem/gcc-ranlib15.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib15"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib15.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib15.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib15"
      - "D:/Git/cmd/gcc-ranlib15.com"
      - "D:/Git/cmd/gcc-ranlib15.exe"
      - "D:/Git/cmd/gcc-ranlib15"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib15.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib15.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib15"
      - "C:/Program Files/dotnet/gcc-ranlib15.com"
      - "C:/Program Files/dotnet/gcc-ranlib15.exe"
      - "C:/Program Files/dotnet/gcc-ranlib15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib15"
      - "E:/Node/gcc-ranlib15.com"
      - "E:/Node/gcc-ranlib15.exe"
      - "E:/Node/gcc-ranlib15"
      - "C:/Program Files/CMake/bin/gcc-ranlib15.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib15.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib15"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib15.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib15.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib15"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib15.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib15.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib15"
      - "E:/Node/node_globle/gcc-ranlib15.com"
      - "E:/Node/node_globle/gcc-ranlib15.exe"
      - "E:/Node/node_globle/gcc-ranlib15"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib15.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib15.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib15"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib15.com"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib15.exe"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib15"
      - "E:/cursor/resources/app/bin/gcc-ranlib15.com"
      - "E:/cursor/resources/app/bin/gcc-ranlib15.exe"
      - "E:/cursor/resources/app/bin/gcc-ranlib15"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib15.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib15.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib15"
      - "E:/opencv/mingw_build/bin/gcc-ranlib15.com"
      - "E:/opencv/mingw_build/bin/gcc-ranlib15.exe"
      - "E:/opencv/mingw_build/bin/gcc-ranlib15"
      - "E:/mingw64/bin/gcc-ranlib.com"
    found: "E:/mingw64/bin/gcc-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake:54 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:69 (_cmake_find_compiler)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER"
    description: "CXX compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "c++"
      - "g++"
      - "cl"
      - "bcc"
      - "icx"
      - "clang++"
    candidate_directories:
      - "E:/mingw64/bin/"
    searched_directories:
      - "E:/mingw64/bin/c++.com"
    found: "E:/mingw64/bin/c++.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: E:/mingw64/bin/c++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is GNU, found in:
        D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/4.1.0/CompilerIdCXX/a.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-15.2"
      - "gcc-ar-15"
      - "gcc-ar15"
      - "gcc-ar"
    candidate_directories:
      - "E:/mingw64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/mingw64/bin/gcc-ar-15.2.com"
      - "E:/mingw64/bin/gcc-ar-15.2.exe"
      - "E:/mingw64/bin/gcc-ar-15.2"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-15.2.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-15.2.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-15.2"
      - "C:/Windows/System32/gcc-ar-15.2.com"
      - "C:/Windows/System32/gcc-ar-15.2.exe"
      - "C:/Windows/System32/gcc-ar-15.2"
      - "C:/Windows/gcc-ar-15.2.com"
      - "C:/Windows/gcc-ar-15.2.exe"
      - "C:/Windows/gcc-ar-15.2"
      - "C:/Windows/System32/wbem/gcc-ar-15.2.com"
      - "C:/Windows/System32/wbem/gcc-ar-15.2.exe"
      - "C:/Windows/System32/wbem/gcc-ar-15.2"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.2.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.2.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.2"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.2.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.2.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.2"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-15.2.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-15.2.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-15.2"
      - "D:/Git/cmd/gcc-ar-15.2.com"
      - "D:/Git/cmd/gcc-ar-15.2.exe"
      - "D:/Git/cmd/gcc-ar-15.2"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-15.2.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-15.2.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-15.2"
      - "C:/Program Files/dotnet/gcc-ar-15.2.com"
      - "C:/Program Files/dotnet/gcc-ar-15.2.exe"
      - "C:/Program Files/dotnet/gcc-ar-15.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar-15.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar-15.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar-15.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar-15.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar-15.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar-15.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar-15.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar-15.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar-15.2"
      - "E:/Node/gcc-ar-15.2.com"
      - "E:/Node/gcc-ar-15.2.exe"
      - "E:/Node/gcc-ar-15.2"
      - "C:/Program Files/CMake/bin/gcc-ar-15.2.com"
      - "C:/Program Files/CMake/bin/gcc-ar-15.2.exe"
      - "C:/Program Files/CMake/bin/gcc-ar-15.2"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar-15.2.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar-15.2.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar-15.2"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-15.2.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-15.2.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-15.2"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.2.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.2.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.2"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.2.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.2.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.2"
      - "E:/Node/node_globle/gcc-ar-15.2.com"
      - "E:/Node/node_globle/gcc-ar-15.2.exe"
      - "E:/Node/node_globle/gcc-ar-15.2"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-15.2.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-15.2.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-15.2"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar-15.2.com"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar-15.2.exe"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar-15.2"
      - "E:/cursor/resources/app/bin/gcc-ar-15.2.com"
      - "E:/cursor/resources/app/bin/gcc-ar-15.2.exe"
      - "E:/cursor/resources/app/bin/gcc-ar-15.2"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar-15.2.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar-15.2.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar-15.2"
      - "E:/opencv/mingw_build/bin/gcc-ar-15.2.com"
      - "E:/opencv/mingw_build/bin/gcc-ar-15.2.exe"
      - "E:/opencv/mingw_build/bin/gcc-ar-15.2"
      - "E:/mingw64/bin/gcc-ar-15.com"
      - "E:/mingw64/bin/gcc-ar-15.exe"
      - "E:/mingw64/bin/gcc-ar-15"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-15.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-15.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar-15"
      - "C:/Windows/System32/gcc-ar-15.com"
      - "C:/Windows/System32/gcc-ar-15.exe"
      - "C:/Windows/System32/gcc-ar-15"
      - "C:/Windows/gcc-ar-15.com"
      - "C:/Windows/gcc-ar-15.exe"
      - "C:/Windows/gcc-ar-15"
      - "C:/Windows/System32/wbem/gcc-ar-15.com"
      - "C:/Windows/System32/wbem/gcc-ar-15.exe"
      - "C:/Windows/System32/wbem/gcc-ar-15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-15.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-15.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar-15"
      - "D:/Git/cmd/gcc-ar-15.com"
      - "D:/Git/cmd/gcc-ar-15.exe"
      - "D:/Git/cmd/gcc-ar-15"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-15.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-15.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar-15"
      - "C:/Program Files/dotnet/gcc-ar-15.com"
      - "C:/Program Files/dotnet/gcc-ar-15.exe"
      - "C:/Program Files/dotnet/gcc-ar-15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar-15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar-15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar-15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar-15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar-15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar-15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar-15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar-15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar-15"
      - "E:/Node/gcc-ar-15.com"
      - "E:/Node/gcc-ar-15.exe"
      - "E:/Node/gcc-ar-15"
      - "C:/Program Files/CMake/bin/gcc-ar-15.com"
      - "C:/Program Files/CMake/bin/gcc-ar-15.exe"
      - "C:/Program Files/CMake/bin/gcc-ar-15"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar-15.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar-15.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar-15"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-15.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-15.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar-15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15"
      - "E:/Node/node_globle/gcc-ar-15.com"
      - "E:/Node/node_globle/gcc-ar-15.exe"
      - "E:/Node/node_globle/gcc-ar-15"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-15.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-15.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar-15"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar-15.com"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar-15.exe"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar-15"
      - "E:/cursor/resources/app/bin/gcc-ar-15.com"
      - "E:/cursor/resources/app/bin/gcc-ar-15.exe"
      - "E:/cursor/resources/app/bin/gcc-ar-15"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar-15.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar-15.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar-15"
      - "E:/opencv/mingw_build/bin/gcc-ar-15.com"
      - "E:/opencv/mingw_build/bin/gcc-ar-15.exe"
      - "E:/opencv/mingw_build/bin/gcc-ar-15"
      - "E:/mingw64/bin/gcc-ar15.com"
      - "E:/mingw64/bin/gcc-ar15.exe"
      - "E:/mingw64/bin/gcc-ar15"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar15.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar15.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ar15"
      - "C:/Windows/System32/gcc-ar15.com"
      - "C:/Windows/System32/gcc-ar15.exe"
      - "C:/Windows/System32/gcc-ar15"
      - "C:/Windows/gcc-ar15.com"
      - "C:/Windows/gcc-ar15.exe"
      - "C:/Windows/gcc-ar15"
      - "C:/Windows/System32/wbem/gcc-ar15.com"
      - "C:/Windows/System32/wbem/gcc-ar15.exe"
      - "C:/Windows/System32/wbem/gcc-ar15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15"
      - "C:/Windows/System32/OpenSSH/gcc-ar15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar15"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar15.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar15.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ar15"
      - "D:/Git/cmd/gcc-ar15.com"
      - "D:/Git/cmd/gcc-ar15.exe"
      - "D:/Git/cmd/gcc-ar15"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar15.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar15.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ar15"
      - "C:/Program Files/dotnet/gcc-ar15.com"
      - "C:/Program Files/dotnet/gcc-ar15.exe"
      - "C:/Program Files/dotnet/gcc-ar15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ar15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ar15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ar15"
      - "E:/Node/gcc-ar15.com"
      - "E:/Node/gcc-ar15.exe"
      - "E:/Node/gcc-ar15"
      - "C:/Program Files/CMake/bin/gcc-ar15.com"
      - "C:/Program Files/CMake/bin/gcc-ar15.exe"
      - "C:/Program Files/CMake/bin/gcc-ar15"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar15.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar15.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ar15"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar15.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar15.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ar15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar15"
      - "E:/Node/node_globle/gcc-ar15.com"
      - "E:/Node/node_globle/gcc-ar15.exe"
      - "E:/Node/node_globle/gcc-ar15"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar15.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar15.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ar15"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar15.com"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar15.exe"
      - "E:/opencv/build/x64/vc16/bin/gcc-ar15"
      - "E:/cursor/resources/app/bin/gcc-ar15.com"
      - "E:/cursor/resources/app/bin/gcc-ar15.exe"
      - "E:/cursor/resources/app/bin/gcc-ar15"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar15.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar15.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ar15"
      - "E:/opencv/mingw_build/bin/gcc-ar15.com"
      - "E:/opencv/mingw_build/bin/gcc-ar15.exe"
      - "E:/opencv/mingw_build/bin/gcc-ar15"
      - "E:/mingw64/bin/gcc-ar.com"
    found: "E:/mingw64/bin/gcc-ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-15.2"
      - "gcc-ranlib-15"
      - "gcc-ranlib15"
      - "gcc-ranlib"
    candidate_directories:
      - "E:/mingw64/bin/"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
    searched_directories:
      - "E:/mingw64/bin/gcc-ranlib-15.2.com"
      - "E:/mingw64/bin/gcc-ranlib-15.2.exe"
      - "E:/mingw64/bin/gcc-ranlib-15.2"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-15.2.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-15.2.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-15.2"
      - "C:/Windows/System32/gcc-ranlib-15.2.com"
      - "C:/Windows/System32/gcc-ranlib-15.2.exe"
      - "C:/Windows/System32/gcc-ranlib-15.2"
      - "C:/Windows/gcc-ranlib-15.2.com"
      - "C:/Windows/gcc-ranlib-15.2.exe"
      - "C:/Windows/gcc-ranlib-15.2"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.2.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.2.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.2"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.2.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.2.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.2"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.2.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.2.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.2"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-15.2.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-15.2.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-15.2"
      - "D:/Git/cmd/gcc-ranlib-15.2.com"
      - "D:/Git/cmd/gcc-ranlib-15.2.exe"
      - "D:/Git/cmd/gcc-ranlib-15.2"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-15.2.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-15.2.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-15.2"
      - "C:/Program Files/dotnet/gcc-ranlib-15.2.com"
      - "C:/Program Files/dotnet/gcc-ranlib-15.2.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-15.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib-15.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib-15.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib-15.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib-15.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib-15.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib-15.2"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib-15.2.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib-15.2.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib-15.2"
      - "E:/Node/gcc-ranlib-15.2.com"
      - "E:/Node/gcc-ranlib-15.2.exe"
      - "E:/Node/gcc-ranlib-15.2"
      - "C:/Program Files/CMake/bin/gcc-ranlib-15.2.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib-15.2.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib-15.2"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib-15.2.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib-15.2.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib-15.2"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-15.2.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-15.2.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-15.2"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.2.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.2.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.2"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.2.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.2.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.2"
      - "E:/Node/node_globle/gcc-ranlib-15.2.com"
      - "E:/Node/node_globle/gcc-ranlib-15.2.exe"
      - "E:/Node/node_globle/gcc-ranlib-15.2"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-15.2.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-15.2.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-15.2"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib-15.2.com"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib-15.2.exe"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib-15.2"
      - "E:/cursor/resources/app/bin/gcc-ranlib-15.2.com"
      - "E:/cursor/resources/app/bin/gcc-ranlib-15.2.exe"
      - "E:/cursor/resources/app/bin/gcc-ranlib-15.2"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib-15.2.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib-15.2.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib-15.2"
      - "E:/opencv/mingw_build/bin/gcc-ranlib-15.2.com"
      - "E:/opencv/mingw_build/bin/gcc-ranlib-15.2.exe"
      - "E:/opencv/mingw_build/bin/gcc-ranlib-15.2"
      - "E:/mingw64/bin/gcc-ranlib-15.com"
      - "E:/mingw64/bin/gcc-ranlib-15.exe"
      - "E:/mingw64/bin/gcc-ranlib-15"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-15.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-15.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib-15"
      - "C:/Windows/System32/gcc-ranlib-15.com"
      - "C:/Windows/System32/gcc-ranlib-15.exe"
      - "C:/Windows/System32/gcc-ranlib-15"
      - "C:/Windows/gcc-ranlib-15.com"
      - "C:/Windows/gcc-ranlib-15.exe"
      - "C:/Windows/gcc-ranlib-15"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-15.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-15.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib-15"
      - "D:/Git/cmd/gcc-ranlib-15.com"
      - "D:/Git/cmd/gcc-ranlib-15.exe"
      - "D:/Git/cmd/gcc-ranlib-15"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-15.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-15.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib-15"
      - "C:/Program Files/dotnet/gcc-ranlib-15.com"
      - "C:/Program Files/dotnet/gcc-ranlib-15.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib-15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib-15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib-15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib-15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib-15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib-15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib-15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib-15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib-15"
      - "E:/Node/gcc-ranlib-15.com"
      - "E:/Node/gcc-ranlib-15.exe"
      - "E:/Node/gcc-ranlib-15"
      - "C:/Program Files/CMake/bin/gcc-ranlib-15.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib-15.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib-15"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib-15.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib-15.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib-15"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-15.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-15.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib-15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15"
      - "E:/Node/node_globle/gcc-ranlib-15.com"
      - "E:/Node/node_globle/gcc-ranlib-15.exe"
      - "E:/Node/node_globle/gcc-ranlib-15"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-15.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-15.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib-15"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib-15.com"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib-15.exe"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib-15"
      - "E:/cursor/resources/app/bin/gcc-ranlib-15.com"
      - "E:/cursor/resources/app/bin/gcc-ranlib-15.exe"
      - "E:/cursor/resources/app/bin/gcc-ranlib-15"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib-15.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib-15.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib-15"
      - "E:/opencv/mingw_build/bin/gcc-ranlib-15.com"
      - "E:/opencv/mingw_build/bin/gcc-ranlib-15.exe"
      - "E:/opencv/mingw_build/bin/gcc-ranlib-15"
      - "E:/mingw64/bin/gcc-ranlib15.com"
      - "E:/mingw64/bin/gcc-ranlib15.exe"
      - "E:/mingw64/bin/gcc-ranlib15"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib15.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib15.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/gcc-ranlib15"
      - "C:/Windows/System32/gcc-ranlib15.com"
      - "C:/Windows/System32/gcc-ranlib15.exe"
      - "C:/Windows/System32/gcc-ranlib15"
      - "C:/Windows/gcc-ranlib15.com"
      - "C:/Windows/gcc-ranlib15.exe"
      - "C:/Windows/gcc-ranlib15"
      - "C:/Windows/System32/wbem/gcc-ranlib15.com"
      - "C:/Windows/System32/wbem/gcc-ranlib15.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib15"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib15.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib15.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/gcc-ranlib15"
      - "D:/Git/cmd/gcc-ranlib15.com"
      - "D:/Git/cmd/gcc-ranlib15.exe"
      - "D:/Git/cmd/gcc-ranlib15"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib15.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib15.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/gcc-ranlib15"
      - "C:/Program Files/dotnet/gcc-ranlib15.com"
      - "C:/Program Files/dotnet/gcc-ranlib15.exe"
      - "C:/Program Files/dotnet/gcc-ranlib15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/gcc-ranlib15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/gcc-ranlib15"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib15.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib15.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/gcc-ranlib15"
      - "E:/Node/gcc-ranlib15.com"
      - "E:/Node/gcc-ranlib15.exe"
      - "E:/Node/gcc-ranlib15"
      - "C:/Program Files/CMake/bin/gcc-ranlib15.com"
      - "C:/Program Files/CMake/bin/gcc-ranlib15.exe"
      - "C:/Program Files/CMake/bin/gcc-ranlib15"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib15.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib15.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/gcc-ranlib15"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib15.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib15.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/gcc-ranlib15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib15"
      - "E:/Node/node_globle/gcc-ranlib15.com"
      - "E:/Node/node_globle/gcc-ranlib15.exe"
      - "E:/Node/node_globle/gcc-ranlib15"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib15.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib15.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/gcc-ranlib15"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib15.com"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib15.exe"
      - "E:/opencv/build/x64/vc16/bin/gcc-ranlib15"
      - "E:/cursor/resources/app/bin/gcc-ranlib15.com"
      - "E:/cursor/resources/app/bin/gcc-ranlib15.exe"
      - "E:/cursor/resources/app/bin/gcc-ranlib15"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib15.com"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib15.exe"
      - "E:/cmake-4.1.1-windows-x86_64/bin/gcc-ranlib15"
      - "E:/opencv/mingw_build/bin/gcc-ranlib15.com"
      - "E:/opencv/mingw_build/bin/gcc-ranlib15.exe"
      - "E:/opencv/mingw_build/bin/gcc-ranlib15"
      - "E:/mingw64/bin/gcc-ranlib.com"
    found: "E:/mingw64/bin/gcc-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU.cmake:167 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU-C.cmake:2 (__windows_compiler_gnu)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCInformation.cmake:48 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "windres"
      - "windres"
    candidate_directories:
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/Git/cmd/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/"
      - "C:/Program Files/dotnet/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/"
      - "E:/Node/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "E:/Node/node_globle/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/opencv/build/x64/vc16/bin/"
      - "E:/cursor/resources/app/bin/"
      - "E:/mingw64/bin/"
      - "E:/cmake-4.1.1-windows-x86_64/bin/"
      - "E:/opencv/mingw_build/bin/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/Rasterizer/bin/"
      - "C:/Program Files (x86)/Rasterizer/sbin/"
      - "C:/Program Files (x86)/Rasterizer/"
    searched_directories:
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/windres.com"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/windres.exe"
      - "C:/Program Files (x86)/Common Files/Intel/Shared Libraries/redist/intel64/compiler/windres"
      - "C:/Windows/System32/windres.com"
      - "C:/Windows/System32/windres.exe"
      - "C:/Windows/System32/windres"
      - "C:/Windows/windres.com"
      - "C:/Windows/windres.exe"
      - "C:/Windows/windres"
      - "C:/Windows/System32/wbem/windres.com"
      - "C:/Windows/System32/wbem/windres.exe"
      - "C:/Windows/System32/wbem/windres"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/windres.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/windres.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/windres"
      - "C:/Windows/System32/OpenSSH/windres.com"
      - "C:/Windows/System32/OpenSSH/windres.exe"
      - "C:/Windows/System32/OpenSSH/windres"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/windres.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/windres.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/windres"
      - "D:/Git/cmd/windres.com"
      - "D:/Git/cmd/windres.exe"
      - "D:/Git/cmd/windres"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/windres.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/windres.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA app/NvDLISR/windres"
      - "C:/Program Files/dotnet/windres.com"
      - "C:/Program Files/dotnet/windres.exe"
      - "C:/Program Files/dotnet/windres"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/windres.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/windres.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_globle/windres"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/windres.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/windres.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/node_cache/windres"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/windres.com"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/windres.exe"
      - "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/%NODE HOME%/windres"
      - "E:/Node/windres.com"
      - "E:/Node/windres.exe"
      - "E:/Node/windres"
      - "C:/Program Files/CMake/bin/windres.com"
      - "C:/Program Files/CMake/bin/windres.exe"
      - "C:/Program Files/CMake/bin/windres"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/windres.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/windres.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/windres"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/windres.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/windres.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/windres"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/windres.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/windres.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/windres"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/windres.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/windres.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/windres"
      - "E:/Node/node_globle/windres.com"
      - "E:/Node/node_globle/windres.exe"
      - "E:/Node/node_globle/windres"
      - "C:/Users/<USER>/AppData/Roaming/npm/windres.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/windres.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/windres"
      - "E:/opencv/build/x64/vc16/bin/windres.com"
      - "E:/opencv/build/x64/vc16/bin/windres.exe"
      - "E:/opencv/build/x64/vc16/bin/windres"
      - "E:/cursor/resources/app/bin/windres.com"
      - "E:/cursor/resources/app/bin/windres.exe"
      - "E:/cursor/resources/app/bin/windres"
      - "E:/mingw64/bin/windres.com"
    found: "E:/mingw64/bin/windres.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "D:\\Git\\cmd"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR"
        - "C:\\Program Files\\dotnet\\"
        - "%NODE HOME%\\node_globle"
        - "%NODE HOME%\\node_cache"
        - "%NODE HOME%"
        - "E:\\Node"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin"
        - "E:\\Node\\node_globle"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\opencv\\build\\x64\\vc16\\bin"
        - "E:\\cursor\\resources\\app\\bin"
        - "E:\\mingw64\\bin"
        - "E:\\cmake-4.1.1-windows-x86_64\\bin"
        - "E:\\opencv\\mingw_build\\bin"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/Rasterizer"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/Rasterizer"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-nardo7"
      binary: "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-nardo7"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-nardo7'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 E:/mingw64/bin/mingw32-make.exe -f Makefile cmTC_3d7e1/fast
        E:/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_3d7e1.dir\\build.make CMakeFiles/cmTC_3d7e1.dir/build
        mingw32-make[1]: Entering directory 'D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-nardo7'
        Building C object CMakeFiles/cmTC_3d7e1.dir/CMakeCCompilerABI.c.obj
        E:\\mingw64\\bin\\cc.exe   -v -o CMakeFiles\\cmTC_3d7e1.dir\\CMakeCCompilerABI.c.obj -c "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
        Using built-in specs.
        COLLECT_GCC=E:\\mingw64\\bin\\cc.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc'
        Thread model: win32
        Supported LTO compression algorithms: zlib
        gcc version 15.2.0 (x86_64-win32-seh-rev0, Built by MinGW-Builds project) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_3d7e1.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_3d7e1.dir\\'
         E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/cc1.exe -quiet -v -iprefix E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/ -U_REENTRANT C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_3d7e1.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=core2 -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccYhQrEy.s
        GNU C23 (x86_64-win32-seh-rev0, Built by MinGW-Builds project) version 15.2.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 15.2.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include"
        ignoring nonexistent directory "C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64D:/a/_temp/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../include"
        ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed"
        ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include
         E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed
         E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        Compiler executable checksum: 095b29ed3549f595ba8c614f295895ef
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_3d7e1.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_3d7e1.dir\\'
         E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_3d7e1.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccYhQrEy.s
        GNU assembler version 2.45 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.45
        COMPILER_PATH=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/;E:/mingw64/bin/../libexec/gcc/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/;E:/mingw64/bin/../lib/gcc/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_3d7e1.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_3d7e1.dir\\CMakeCCompilerABI.c.'
        Linking C executable cmTC_3d7e1.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_3d7e1.dir\\link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=E:\\mingw64\\bin\\cc.exe
        COLLECT_LTO_WRAPPER=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc'
        Thread model: win32
        Supported LTO compression algorithms: zlib
        gcc version 15.2.0 (x86_64-win32-seh-rev0, Built by MinGW-Builds project) 
        COMPILER_PATH=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/;E:/mingw64/bin/../libexec/gcc/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/;E:/mingw64/bin/../lib/gcc/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_3d7e1.exe' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_3d7e1.'
         E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/collect2.exe -plugin E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll -plugin-opt=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccmwLjaP.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 --sysroot=C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_3d7e1.exe E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0 -LE:/mingw64/bin/../lib/gcc -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_3d7e1.dir/objects.a --no-whole-archive --out-implib libcmTC_3d7e1.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o
        collect2 version 15.2.0
        E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll -plugin-opt=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccmwLjaP.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 --sysroot=C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_3d7e1.exe E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0 -LE:/mingw64/bin/../lib/gcc -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_3d7e1.dir/objects.a --no-whole-archive --out-implib libcmTC_3d7e1.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o
        GNU ld (GNU Binutils) 2.45
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_3d7e1.exe' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_3d7e1.'
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_3d7e1.dir/objects.a
        E:\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_3d7e1.dir/objects.a @CMakeFiles\\cmTC_3d7e1.dir\\objects1.rsp
        E:\\mingw64\\bin\\cc.exe  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_3d7e1.dir/objects.a -Wl,--no-whole-archive -o cmTC_3d7e1.exe -Wl,--out-implib,libcmTC_3d7e1.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        mingw32-make[1]: Leaving directory 'D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-nardo7'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include]
          add: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed]
          add: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include] ==> [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include]
        collapse include dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed] ==> [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed]
        collapse include dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include] ==> [E:/mingw64/x86_64-w64-mingw32/include]
        implicit include dirs: [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include;E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed;E:/mingw64/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-nardo7']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 E:/mingw64/bin/mingw32-make.exe -f Makefile cmTC_3d7e1/fast]
        ignore line: [E:/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_3d7e1.dir\\build.make CMakeFiles/cmTC_3d7e1.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-nardo7']
        ignore line: [Building C object CMakeFiles/cmTC_3d7e1.dir/CMakeCCompilerABI.c.obj]
        ignore line: [E:\\mingw64\\bin\\cc.exe   -v -o CMakeFiles\\cmTC_3d7e1.dir\\CMakeCCompilerABI.c.obj -c "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=E:\\mingw64\\bin\\cc.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc']
        ignore line: [Thread model: win32]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.2.0 (x86_64-win32-seh-rev0  Built by MinGW-Builds project) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_3d7e1.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_3d7e1.dir\\']
        ignore line: [ E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/cc1.exe -quiet -v -iprefix E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/ -U_REENTRANT C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_3d7e1.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=core2 -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccYhQrEy.s]
        ignore line: [GNU C23 (x86_64-win32-seh-rev0  Built by MinGW-Builds project) version 15.2.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 15.2.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include"]
        ignore line: [ignoring nonexistent directory "C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64D:/a/_temp/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../include"]
        ignore line: [ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed"]
        ignore line: [ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include]
        ignore line: [ E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed]
        ignore line: [ E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: 095b29ed3549f595ba8c614f295895ef]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_3d7e1.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_3d7e1.dir\\']
        ignore line: [ E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_3d7e1.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccYhQrEy.s]
        ignore line: [GNU assembler version 2.45 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.45]
        ignore line: [COMPILER_PATH=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [E:/mingw64/bin/../libexec/gcc/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [E:/mingw64/bin/../lib/gcc/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_3d7e1.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_3d7e1.dir\\CMakeCCompilerABI.c.']
        ignore line: [Linking C executable cmTC_3d7e1.exe]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_3d7e1.dir\\link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=E:\\mingw64\\bin\\cc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc']
        ignore line: [Thread model: win32]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.2.0 (x86_64-win32-seh-rev0  Built by MinGW-Builds project) ]
        ignore line: [COMPILER_PATH=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [E:/mingw64/bin/../libexec/gcc/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [E:/mingw64/bin/../lib/gcc/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_3d7e1.exe' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_3d7e1.']
        link line: [ E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/collect2.exe -plugin E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll -plugin-opt=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccmwLjaP.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 --sysroot=C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_3d7e1.exe E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0 -LE:/mingw64/bin/../lib/gcc -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_3d7e1.dir/objects.a --no-whole-archive --out-implib libcmTC_3d7e1.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
          arg [E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccmwLjaP.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-liconv] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [--sysroot=C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_3d7e1.exe] ==> ignore
          arg [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o] ==> obj [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o]
          arg [-LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0] ==> dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0]
          arg [-LE:/mingw64/bin/../lib/gcc] ==> dir [E:/mingw64/bin/../lib/gcc]
          arg [-LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib] ==> dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib]
          arg [-LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib] ==> dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../..] ==> dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_3d7e1.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_3d7e1.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-liconv] ==> lib [iconv]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o] ==> obj [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
        linker tool for 'C': ../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe
        remove lib [gcc_eh]
        remove lib [msvcrt]
        remove lib [gcc_eh]
        remove lib [msvcrt]
        collapse obj [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [E:/mingw64/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o] ==> [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o]
        collapse obj [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o] ==> [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
        collapse library dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0] ==> [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0]
        collapse library dir [E:/mingw64/bin/../lib/gcc] ==> [E:/mingw64/lib/gcc]
        collapse library dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [E:/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib] ==> [E:/mingw64/lib]
        collapse library dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib] ==> [E:/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../..] ==> [E:/mingw64/lib]
        implicit libs: [mingw32;gcc;mingwex;kernel32;advapi32;shell32;user32;kernel32;iconv;mingw32;gcc;mingwex;kernel32]
        implicit objs: [E:/mingw64/x86_64-w64-mingw32/lib/crt2.o;E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o;E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
        implicit dirs: [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0;E:/mingw64/lib/gcc;E:/mingw64/x86_64-w64-mingw32/lib;E:/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe" "-v"
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe" "-V"
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe" "--version"
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-zxc8lz"
      binary: "D:/pet/CODE/\u56fe\u5f62/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-zxc8lz"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-zxc8lz'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 E:/mingw64/bin/mingw32-make.exe -f Makefile cmTC_0b163/fast
        E:/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_0b163.dir\\build.make CMakeFiles/cmTC_0b163.dir/build
        mingw32-make[1]: Entering directory 'D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-zxc8lz'
        Building CXX object CMakeFiles/cmTC_0b163.dir/CMakeCXXCompilerABI.cpp.obj
        E:\\mingw64\\bin\\c++.exe   -v -o CMakeFiles\\cmTC_0b163.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
        Using built-in specs.
        COLLECT_GCC=E:\\mingw64\\bin\\c++.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc'
        Thread model: win32
        Supported LTO compression algorithms: zlib
        gcc version 15.2.0 (x86_64-win32-seh-rev0, Built by MinGW-Builds project) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_0b163.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_0b163.dir\\'
         E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/cc1plus.exe -quiet -v -iprefix E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/ -U_REENTRANT C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles\\cmTC_0b163.dir\\ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=core2 -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccUru9Ed.s
        GNU C++17 (x86_64-win32-seh-rev0, Built by MinGW-Builds project) version 15.2.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 15.2.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++"
        ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/x86_64-w64-mingw32"
        ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/backward"
        ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include"
        ignoring nonexistent directory "C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64D:/a/_temp/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../include"
        ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed"
        ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++
         E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/x86_64-w64-mingw32
         E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/backward
         E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include
         E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed
         E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        Compiler executable checksum: d36d25bbc8758db73b3fef2a044ea176
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_0b163.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_0b163.dir\\'
         E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_0b163.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccUru9Ed.s
        GNU assembler version 2.45 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.45
        COMPILER_PATH=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/;E:/mingw64/bin/../libexec/gcc/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/;E:/mingw64/bin/../lib/gcc/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_0b163.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_0b163.dir\\CMakeCXXCompilerABI.cpp.'
        Linking CXX executable cmTC_0b163.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_0b163.dir\\link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=E:\\mingw64\\bin\\c++.exe
        COLLECT_LTO_WRAPPER=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc'
        Thread model: win32
        Supported LTO compression algorithms: zlib
        gcc version 15.2.0 (x86_64-win32-seh-rev0, Built by MinGW-Builds project) 
        COMPILER_PATH=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/;E:/mingw64/bin/../libexec/gcc/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/;E:/mingw64/bin/../lib/gcc/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/;E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_0b163.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_0b163.'
         E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/collect2.exe -plugin E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll -plugin-opt=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccrOYxvj.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 --sysroot=C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_0b163.exe E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0 -LE:/mingw64/bin/../lib/gcc -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_0b163.dir/objects.a --no-whole-archive --out-implib libcmTC_0b163.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o
        collect2 version 15.2.0
        E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll -plugin-opt=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccrOYxvj.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 --sysroot=C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_0b163.exe E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0 -LE:/mingw64/bin/../lib/gcc -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_0b163.dir/objects.a --no-whole-archive --out-implib libcmTC_0b163.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o
        GNU ld (GNU Binutils) 2.45
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_0b163.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_0b163.'
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_0b163.dir/objects.a
        E:\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_0b163.dir/objects.a @CMakeFiles\\cmTC_0b163.dir\\objects1.rsp
        E:\\mingw64\\bin\\c++.exe  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_0b163.dir/objects.a -Wl,--no-whole-archive -o cmTC_0b163.exe -Wl,--out-implib,libcmTC_0b163.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        mingw32-make[1]: Leaving directory 'D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-zxc8lz'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++]
          add: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/x86_64-w64-mingw32]
          add: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/backward]
          add: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include]
          add: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed]
          add: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++] ==> [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++]
        collapse include dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/x86_64-w64-mingw32] ==> [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/x86_64-w64-mingw32]
        collapse include dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/backward] ==> [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/backward]
        collapse include dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include] ==> [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include]
        collapse include dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed] ==> [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed]
        collapse include dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include] ==> [E:/mingw64/x86_64-w64-mingw32/include]
        implicit include dirs: [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++;E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/x86_64-w64-mingw32;E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/backward;E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include;E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed;E:/mingw64/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-zxc8lz']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 E:/mingw64/bin/mingw32-make.exe -f Makefile cmTC_0b163/fast]
        ignore line: [E:/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_0b163.dir\\build.make CMakeFiles/cmTC_0b163.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'D:/pet/CODE/图形/HHMM/Hw3/build/CMakeFiles/CMakeScratch/TryCompile-zxc8lz']
        ignore line: [Building CXX object CMakeFiles/cmTC_0b163.dir/CMakeCXXCompilerABI.cpp.obj]
        ignore line: [E:\\mingw64\\bin\\c++.exe   -v -o CMakeFiles\\cmTC_0b163.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=E:\\mingw64\\bin\\c++.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc']
        ignore line: [Thread model: win32]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.2.0 (x86_64-win32-seh-rev0  Built by MinGW-Builds project) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_0b163.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_0b163.dir\\']
        ignore line: [ E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/cc1plus.exe -quiet -v -iprefix E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/ -U_REENTRANT C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles\\cmTC_0b163.dir\\ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=core2 -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccUru9Ed.s]
        ignore line: [GNU C++17 (x86_64-win32-seh-rev0  Built by MinGW-Builds project) version 15.2.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 15.2.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++"]
        ignore line: [ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/x86_64-w64-mingw32"]
        ignore line: [ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/backward"]
        ignore line: [ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include"]
        ignore line: [ignoring nonexistent directory "C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64D:/a/_temp/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../include"]
        ignore line: [ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed"]
        ignore line: [ignoring duplicate directory "E:/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++]
        ignore line: [ E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/x86_64-w64-mingw32]
        ignore line: [ E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include/c++/backward]
        ignore line: [ E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include]
        ignore line: [ E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed]
        ignore line: [ E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: d36d25bbc8758db73b3fef2a044ea176]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_0b163.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_0b163.dir\\']
        ignore line: [ E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_0b163.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccUru9Ed.s]
        ignore line: [GNU assembler version 2.45 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.45]
        ignore line: [COMPILER_PATH=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [E:/mingw64/bin/../libexec/gcc/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [E:/mingw64/bin/../lib/gcc/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_0b163.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_0b163.dir\\CMakeCXXCompilerABI.cpp.']
        ignore line: [Linking CXX executable cmTC_0b163.exe]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_0b163.dir\\link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=E:\\mingw64\\bin\\c++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc']
        ignore line: [Thread model: win32]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.2.0 (x86_64-win32-seh-rev0  Built by MinGW-Builds project) ]
        ignore line: [COMPILER_PATH=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [E:/mingw64/bin/../libexec/gcc/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [E:/mingw64/bin/../lib/gcc/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_0b163.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_0b163.']
        link line: [ E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/collect2.exe -plugin E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll -plugin-opt=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccrOYxvj.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 --sysroot=C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_0b163.exe E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0 -LE:/mingw64/bin/../lib/gcc -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib -LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_0b163.dir/objects.a --no-whole-archive --out-implib libcmTC_0b163.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
          arg [E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=E:/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccrOYxvj.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-liconv] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [--sysroot=C:/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_0b163.exe] ==> ignore
          arg [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o] ==> obj [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o]
          arg [-LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0] ==> dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0]
          arg [-LE:/mingw64/bin/../lib/gcc] ==> dir [E:/mingw64/bin/../lib/gcc]
          arg [-LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib] ==> dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib]
          arg [-LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib] ==> dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LE:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../..] ==> dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_0b163.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_0b163.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-liconv] ==> lib [iconv]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o] ==> obj [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
        linker tool for 'CXX': ../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [E:/mingw64/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o] ==> [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o]
        collapse obj [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o] ==> [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
        collapse library dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0] ==> [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0]
        collapse library dir [E:/mingw64/bin/../lib/gcc] ==> [E:/mingw64/lib/gcc]
        collapse library dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [E:/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib] ==> [E:/mingw64/lib]
        collapse library dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib] ==> [E:/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [E:/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../..] ==> [E:/mingw64/lib]
        implicit libs: [stdc++;mingw32;gcc_s;gcc;mingwex;kernel32;advapi32;shell32;user32;kernel32;iconv;mingw32;gcc_s;gcc;mingwex;kernel32]
        implicit objs: [E:/mingw64/x86_64-w64-mingw32/lib/crt2.o;E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o;E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
        implicit dirs: [E:/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0;E:/mingw64/lib/gcc;E:/mingw64/x86_64-w64-mingw32/lib;E:/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe" "-v"
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe" "-V"
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "../src/gcc-15.2.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1520-win32-seh-ucrt-rt_v13-rev0/mingw64/bin/ld.exe" "--version"
      
...
