{"cmake": {"generator": {"multiConfig": false, "name": "MinGW Makefiles"}, "paths": {"cmake": "C:/Program Files/CMake/bin/cmake.exe", "cpack": "C:/Program Files/CMake/bin/cpack.exe", "ctest": "C:/Program Files/CMake/bin/ctest.exe", "root": "C:/Program Files/CMake/share/cmake-4.1"}, "version": {"isDirty": false, "major": 4, "minor": 1, "patch": 0, "string": "4.1.0", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-cf8f27c4019468270004.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-3c9719339050c04e3e2d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-ea64a4b98ed9b3cb4734.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-d7aa6e75fe850c3099c8.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-3c9719339050c04e3e2d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-cf8f27c4019468270004.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-d7aa6e75fe850c3099c8.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-ea64a4b98ed9b3cb4734.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}