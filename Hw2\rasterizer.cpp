// clang-format off
//
// Created by goksu on 4/6/19.
//

#include <algorithm>
#include <vector>
#include "rasterizer.hpp"
#include <opencv2/opencv.hpp>
#include <math.h>


rst::pos_buf_id rst::rasterizer::load_positions(const std::vector<Eigen::Vector3f> &positions)
{
    auto id = get_next_id();
    pos_buf.emplace(id, positions);

    return {id};
}

rst::ind_buf_id rst::rasterizer::load_indices(const std::vector<Eigen::Vector3i> &indices)
{
    auto id = get_next_id();
    ind_buf.emplace(id, indices);

    return {id};
}

rst::col_buf_id rst::rasterizer::load_colors(const std::vector<Eigen::Vector3f> &cols)
{
    auto id = get_next_id();
    col_buf.emplace(id, cols);

    return {id};
}

auto to_vec4(const Eigen::Vector3f& v3, float w = 1.0f)
{
    return Vector4f(v3.x(), v3.y(), v3.z(), w);
}


// 重载版本：支持整数坐标（原始版本）
static bool insideTriangle(int x, int y, const Vector3f* _v)
{   
    return insideTriangle(float(x), float(y), _v);
}

// 重载版本：支持浮点数坐标（超采样版本）
static bool insideTriangle(float x, float y, const Vector3f* _v)
{   
    // 实现这个函数来检查点 (x, y) 是否在由 v[0], _v[1], _v[2] 表示的三角形内部
    // 叉乘判断：

    // 定义三角形的三个边向量
    Vector3f v0v1 = _v[1] - _v[0];
    Vector3f v1v2 = _v[2] - _v[1];
    Vector3f v2v0 = _v[0] - _v[2];

    // 定义点 p
    Vector3f p = Vector3f(x, y, 1);

    // 定义点 p 到三角形三个顶点的向量
    Vector3f v0p = p - _v[0];
    Vector3f v1p = p - _v[1];
    Vector3f v2p = p - _v[2];
    
    // 计算叉乘结果
    Vector3f cross_product = v0v1.cross(v0p);
    Vector3f cross_product2 = v1v2.cross(v1p);
    Vector3f cross_product3 = v2v0.cross(v2p);

    // 判断叉乘结果的符号
    if (cross_product.z() > 0 && cross_product2.z() > 0 && cross_product3.z() > 0) {
        return true;
    }
    return false;
}

static std::tuple<float, float, float> computeBarycentric2D(float x, float y, const Vector3f* v)
{
    float c1 = (x*(v[1].y() - v[2].y()) + (v[2].x() - v[1].x())*y + v[1].x()*v[2].y() - v[2].x()*v[1].y()) / (v[0].x()*(v[1].y() - v[2].y()) + (v[2].x() - v[1].x())*v[0].y() + v[1].x()*v[2].y() - v[2].x()*v[1].y());
    float c2 = (x*(v[2].y() - v[0].y()) + (v[0].x() - v[2].x())*y + v[2].x()*v[0].y() - v[0].x()*v[2].y()) / (v[1].x()*(v[2].y() - v[0].y()) + (v[0].x() - v[2].x())*v[1].y() + v[2].x()*v[0].y() - v[0].x()*v[2].y());
    float c3 = (x*(v[0].y() - v[1].y()) + (v[1].x() - v[0].x())*y + v[0].x()*v[1].y() - v[1].x()*v[0].y()) / (v[2].x()*(v[0].y() - v[1].y()) + (v[1].x() - v[0].x())*v[2].y() + v[0].x()*v[1].y() - v[1].x()*v[0].y());
    return {c1,c2,c3};
}

void rst::rasterizer::draw(pos_buf_id pos_buffer, ind_buf_id ind_buffer, col_buf_id col_buffer, Primitive type)
{
    auto& buf = pos_buf[pos_buffer.pos_id];
    auto& ind = ind_buf[ind_buffer.ind_id];
    auto& col = col_buf[col_buffer.col_id];

    float f1 = (50 - 0.1) / 2.0;
    float f2 = (50 + 0.1) / 2.0;

    Eigen::Matrix4f mvp = projection * view * model;
    for (auto& i : ind)
    {
        Triangle t;
        Eigen::Vector4f v[] = {
                mvp * to_vec4(buf[i[0]], 1.0f),
                mvp * to_vec4(buf[i[1]], 1.0f),
                mvp * to_vec4(buf[i[2]], 1.0f)
        };
        //Homogeneous division
        for (auto& vec : v) {
            vec /= vec.w();
        }
        //Viewport transformation
        for (auto & vert : v)
        {
            vert.x() = 0.5*width*(vert.x()+1.0);
            vert.y() = 0.5*height*(vert.y()+1.0);
            vert.z() = vert.z() * f1 + f2;
        }

        for (int i = 0; i < 3; ++i)
        {
            t.setVertex(i, v[i].head<3>());
            t.setVertex(i, v[i].head<3>());
            t.setVertex(i, v[i].head<3>());
        }

        auto col_x = col[i[0]];
        auto col_y = col[i[1]];
        auto col_z = col[i[2]];

        t.setColor(0, col_x[0], col_x[1], col_x[2]);
        t.setColor(1, col_y[0], col_y[1], col_y[2]);
        t.setColor(2, col_z[0], col_z[1], col_z[2]);

        rasterize_triangle(t);
    }
}

//Screen space rasterization
void rst::rasterizer::rasterize_triangle(const Triangle& t) {
    // 如果启用超采样，调用超采样版本
    if (use_super_sampling) {
        rasterize_triangle_with_super_sampling(t);
        return;
    }
    
    // 原始版本，保持不变
    auto v = t.toVector4();
    
    // TODO : Find out the bounding box of current triangle.
    // iterate through the pixel and find if the current pixel is inside the triangle

    // If so, use the following code to get the interpolated z value.
    //auto[alpha, beta, gamma] = computeBarycentric2D(x, y, t.v);
    //float w_reciprocal = 1.0/(alpha / v[0].w() + beta / v[1].w() + gamma / v[2].w());
    //float z_interpolated = alpha * v[0].z() / v[0].w() + beta * v[1].z() / v[1].w() + gamma * v[2].z() / v[2].w();
    //z_interpolated *= w_reciprocal;

    // TODO : set the current pixel (use the set_pixel function) to the color of the triangle (use getColor function) if it should be painted.

    // TODO : 找出当前三角形的包围盒
    // 遍历像素并判断当前像素是否在三角形内部

    // 如果在三角形内，使用以下代码获取插值后的z值
    //auto[alpha, beta, gamma] = computeBarycentric2D(x, y, t.v);
    //float w_reciprocal = 1.0/(alpha / v[0].w() + beta / v[1].w() + gamma / v[2].w());
    //float z_interpolated = alpha * v[0].z() / v[0].w() + beta * v[1].z() / v[1].w() + gamma * v[2].z() / v[2].w();
    //z_interpolated *= w_reciprocal;

    // TODO : 如果像素应该被绘制，将当前像素（使用set_pixel函数）设置为三角形的颜色（使用getColor函数）

    // 遍历三角形的三个顶点 获取包围盒
    int min_x = std::min(t.v[0].x(), std::min(t.v[1].x(), t.v[2].x()));
    int min_y = std::min(t.v[0].y(), std::min(t.v[1].y(), t.v[2].y()));
    int max_x = std::max(t.v[0].x(), std::max(t.v[1].x(), t.v[2].x()));
    int max_y = std::max(t.v[0].y(), std::max(t.v[1].y(), t.v[2].y()));

    // 遍历包围盒中的每个像素
    for (int x = min_x; x <= max_x; x++) {
        for (int y = min_y; y <= max_y; y++) {
            // 判断当前像素是否在三角形内部 如果不在 则跳过
            if (!insideTriangle(x, y, t.v)) {
                continue;
            }
            // 插值后z值
            auto[alpha, beta, gamma] = computeBarycentric2D(x, y, t.v);
            float w_reciprocal = 1.0 / (alpha / v[0].w() + beta / v[1].w() + gamma / v[2].w());
            float z_interpolated = alpha * v[0].z() / v[0].w() + beta * v[1].z() / v[1].w() + gamma * v[2].z() / v[2].w();
            z_interpolated *= w_reciprocal;
            
            if(z_interpolated < depth_buf[get_index(x, y)]) {
                depth_buf[get_index(x, y)] = z_interpolated;
                set_pixel(Eigen::Vector3f(x, y, 1), t.getColor());
            }
        }
    }
}

// 超采样版本的三角形光栅化
void rst::rasterizer::rasterize_triangle_with_super_sampling(const Triangle& t) {
    auto v = t.toVector4();
    
    // 遍历三角形的三个顶点 获取包围盒
    int min_x = std::min(t.v[0].x(), std::min(t.v[1].x(), t.v[2].x()));
    int min_y = std::min(t.v[0].y(), std::min(t.v[1].y(), t.v[2].y()));
    int max_x = std::max(t.v[0].x(), std::max(t.v[1].x(), t.v[2].x()));
    int max_y = std::max(t.v[0].y(), std::max(t.v[1].y(), t.v[2].y()));

    // 遍历包围盒中的每个像素
    for (int x = min_x; x <= max_x; x++) {
        for (int y = min_y; y <= max_y; y++) {
            int pixel_index = get_index(x, y);
            
            // 对每个像素进行2x2采样
            for (int i = 0; i < 2; i++) {
                for (int j = 0; j < 2; j++) {
                    // 计算样本点位置 (在像素内的偏移)
                    float sample_x = x + (i + 0.25f) * 0.5f;  // 0.25, 0.75
                    float sample_y = y + (j + 0.25f) * 0.5f;  // 0.25, 0.75
                    
                    // 判断样本点是否在三角形内部
                    if (!insideTriangle(sample_x, sample_y, t.v)) {
                        continue;
                    }
                    
                    // 计算样本点的深度值
                    auto[alpha, beta, gamma] = computeBarycentric2D(sample_x, sample_y, t.v);
                    float w_reciprocal = 1.0 / (alpha / v[0].w() + beta / v[1].w() + gamma / v[2].w());
                    float z_interpolated = alpha * v[0].z() / v[0].w() + beta * v[1].z() / v[1].w() + gamma * v[2].z() / v[2].w();
                    z_interpolated *= w_reciprocal;
                    
                    int sample_idx = i * 2 + j;  // 样本索引 0-3
                    
                    // 深度测试
                    if (z_interpolated < sample_depth_buf[pixel_index][sample_idx]) {
                        sample_depth_buf[pixel_index][sample_idx] = z_interpolated;
                        sample_color_buf[pixel_index][sample_idx] = t.getColor();
                    }
                }
            }
            
            // 计算该像素的最终颜色（4个样本的平均值）
            Eigen::Vector3f final_color = Eigen::Vector3f::Zero();
            for (int i = 0; i < 4; i++) {
                final_color += sample_color_buf[pixel_index][i];
            }
            final_color /= 4.0f;
            
            // 设置像素颜色
            set_pixel(Eigen::Vector3f(x, y, 1), final_color);
        }
    }
}

void rst::rasterizer::set_model(const Eigen::Matrix4f& m)
{
    model = m;
}

void rst::rasterizer::set_view(const Eigen::Matrix4f& v)
{
    view = v;
}

void rst::rasterizer::set_projection(const Eigen::Matrix4f& p)
{
    projection = p;
}

void rst::rasterizer::clear(rst::Buffers buff)
{
    if ((buff & rst::Buffers::Color) == rst::Buffers::Color)
    {
        std::fill(frame_buf.begin(), frame_buf.end(), Eigen::Vector3f{0, 0, 0});
        
        // 清空超采样颜色缓冲区
        if (use_super_sampling) {
            for (auto& pixel_samples : sample_color_buf) {
                std::fill(pixel_samples.begin(), pixel_samples.end(), Eigen::Vector3f(0, 0, 0));
            }
        }
    }
    if ((buff & rst::Buffers::Depth) == rst::Buffers::Depth)
    {
        std::fill(depth_buf.begin(), depth_buf.end(), std::numeric_limits<float>::infinity());
        
        // 清空超采样深度缓冲区
        if (use_super_sampling) {
            for (auto& pixel_samples : sample_depth_buf) {
                std::fill(pixel_samples.begin(), pixel_samples.end(), std::numeric_limits<float>::infinity());
            }
        }
    }
}

rst::rasterizer::rasterizer(int w, int h) : width(w), height(h)
{
    frame_buf.resize(w * h);
    depth_buf.resize(w * h);
    
    // 初始化超采样缓冲区
    sample_depth_buf.resize(w * h);
    sample_color_buf.resize(w * h);
    for (int i = 0; i < w * h; i++) {
        sample_depth_buf[i].resize(4, std::numeric_limits<float>::infinity());
        sample_color_buf[i].resize(4, Eigen::Vector3f(0, 0, 0));
    }
}

int rst::rasterizer::get_index(int x, int y)
{
    return (height-1-y)*width + x;
}

void rst::rasterizer::set_pixel(const Eigen::Vector3f& point, const Eigen::Vector3f& color)
{
    //old index: auto ind = point.y() + point.x() * width;
    auto ind = (height-1-point.y())*width + point.x();
    frame_buf[ind] = color;

}

// clang-format on