cmake_minimum_required(VERSION 3.10)
project(Rasterizer)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Set OpenCV and Eigen paths
set(OpenCV_DIR "E:/OpenCV/opencv/mingw_build")
set(EIGEN3_INCLUDE_DIR "E:/eigen-3.4.0")

# Find OpenCV
find_package(OpenCV REQUIRED PATHS ${OpenCV_DIR} NO_DEFAULT_PATH)

# Include directories
include_directories(${OpenCV_INCLUDE_DIRS})
include_directories(${EIGEN3_INCLUDE_DIR})

# Add executable
add_executable(Rasterizer main.cpp rasterizer.hpp rasterizer.cpp global.hpp OBJ_Loader.h Shader.hpp Texture.hpp Texture.cpp Triangle.hpp Triangle.cpp)

# Link libraries
target_link_libraries(Rasterizer ${OpenCV_LIBRARIES})
