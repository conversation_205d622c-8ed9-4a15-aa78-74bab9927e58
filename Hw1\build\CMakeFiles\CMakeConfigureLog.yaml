
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      适用于 .NET Framework MSBuild 版本 17.14.14+a129329f1
      生成启动时间为 2025/7/28 15:06:56。
      
      节点 1 上的项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\3.30.0-rc1\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
        正在创建目录“Debug\\CompilerIdC.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        正在对“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”执行 Touch 任务。
      ClCompile:
        E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\3.30.0-rc1\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate”执行 Touch 任务。
      已完成生成项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\3.30.0-rc1\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:04.29
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        E:/PETlab/图形/hw/HHMM/Hw1/build/CMakeFiles/3.30.0-rc1/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      适用于 .NET Framework MSBuild 版本 17.14.14+a129329f1
      生成启动时间为 2025/7/28 15:07:01。
      
      节点 1 上的项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\3.30.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
        正在创建目录“Debug\\CompilerIdCXX.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        正在对“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”执行 Touch 任务。
      ClCompile:
        E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\3.30.0-rc1\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate”执行 Touch 任务。
      已完成生成项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\3.30.0-rc1\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:02.55
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        E:/PETlab/图形/hw/HHMM/Hw1/build/CMakeFiles/3.30.0-rc1/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "E:/PETlab/\u56fe\u5f62/hw/HHMM/Hw1/build/CMakeFiles/CMakeScratch/TryCompile-psplub"
      binary: "E:/PETlab/\u56fe\u5f62/hw/HHMM/Hw1/build/CMakeFiles/CMakeScratch/TryCompile-psplub"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/PETlab/图形/hw/HHMM/Hw1/build/CMakeFiles/CMakeScratch/TryCompile-psplub'
        
        Run Build Command(s): E:/VS/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_355cf.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.14+a129329f1
        生成启动时间为 2025/7/28 15:07:05。
        
        节点 1 上的项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\CMakeScratch\\TryCompile-psplub\\cmTC_355cf.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_355cf.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\CMakeScratch\\TryCompile-psplub\\Debug\\”。
          正在创建目录“cmTC_355cf.dir\\Debug\\cmTC_355cf.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_355cf.dir\\Debug\\cmTC_355cf.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_355cf.dir\\Debug\\cmTC_355cf.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_355cf.dir\\Debug\\\\" /Fd"cmTC_355cf.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "E:\\Cmake\\share\\cmake-3.30\\Modules\\CMakeCCompilerABI.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35213 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_355cf.dir\\Debug\\\\" /Fd"cmTC_355cf.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "E:\\Cmake\\share\\cmake-3.30\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\CMakeScratch\\TryCompile-psplub\\Debug\\cmTC_355cf.exe" /INCREMENTAL /ILK:"cmTC_355cf.dir\\Debug\\cmTC_355cf.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/PETlab/图形/hw/HHMM/Hw1/build/CMakeFiles/CMakeScratch/TryCompile-psplub/Debug/cmTC_355cf.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/PETlab/图形/hw/HHMM/Hw1/build/CMakeFiles/CMakeScratch/TryCompile-psplub/Debug/cmTC_355cf.lib" /MACHINE:X64  /machine:x64 cmTC_355cf.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_355cf.vcxproj -> E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\CMakeScratch\\TryCompile-psplub\\Debug\\cmTC_355cf.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_355cf.dir\\Debug\\cmTC_355cf.tlog\\unsuccessfulbuild”。
          正在对“cmTC_355cf.dir\\Debug\\cmTC_355cf.tlog\\cmTC_355cf.lastbuildstate”执行 Touch 任务。
        已完成生成项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\CMakeScratch\\TryCompile-psplub\\cmTC_355cf.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:02.22
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': E:/VS/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "E:/VS/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35213.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "E:/PETlab/\u56fe\u5f62/hw/HHMM/Hw1/build/CMakeFiles/CMakeScratch/TryCompile-lpzzdl"
      binary: "E:/PETlab/\u56fe\u5f62/hw/HHMM/Hw1/build/CMakeFiles/CMakeScratch/TryCompile-lpzzdl"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/PETlab/图形/hw/HHMM/Hw1/build/CMakeFiles/CMakeScratch/TryCompile-lpzzdl'
        
        Run Build Command(s): E:/VS/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_8efd9.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.14+a129329f1
        生成启动时间为 2025/7/28 15:07:08。
        
        节点 1 上的项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lpzzdl\\cmTC_8efd9.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_8efd9.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lpzzdl\\Debug\\”。
          正在创建目录“cmTC_8efd9.dir\\Debug\\cmTC_8efd9.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_8efd9.dir\\Debug\\cmTC_8efd9.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_8efd9.dir\\Debug\\cmTC_8efd9.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_8efd9.dir\\Debug\\\\" /Fd"cmTC_8efd9.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "E:\\Cmake\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35213 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_8efd9.dir\\Debug\\\\" /Fd"cmTC_8efd9.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "E:\\Cmake\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          E:\\VS\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lpzzdl\\Debug\\cmTC_8efd9.exe" /INCREMENTAL /ILK:"cmTC_8efd9.dir\\Debug\\cmTC_8efd9.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/PETlab/图形/hw/HHMM/Hw1/build/CMakeFiles/CMakeScratch/TryCompile-lpzzdl/Debug/cmTC_8efd9.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/PETlab/图形/hw/HHMM/Hw1/build/CMakeFiles/CMakeScratch/TryCompile-lpzzdl/Debug/cmTC_8efd9.lib" /MACHINE:X64  /machine:x64 cmTC_8efd9.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_8efd9.vcxproj -> E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lpzzdl\\Debug\\cmTC_8efd9.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_8efd9.dir\\Debug\\cmTC_8efd9.tlog\\unsuccessfulbuild”。
          正在对“cmTC_8efd9.dir\\Debug\\cmTC_8efd9.tlog\\cmTC_8efd9.lastbuildstate”执行 Touch 任务。
        已完成生成项目“E:\\PETlab\\图形\\hw\\HHMM\\Hw1\\build\\CMakeFiles\\CMakeScratch\\TryCompile-lpzzdl\\cmTC_8efd9.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:02.37
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': E:/VS/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "E:/Cmake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "E:/Cmake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "E:/VS/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35213.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
