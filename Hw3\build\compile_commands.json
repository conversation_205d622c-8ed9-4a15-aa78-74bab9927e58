[{"directory": "D:/pet/CODE/图形/HHMM/Hw3/build", "command": "E:\\mingw64\\bin\\g++.exe  @CMakeFiles/Rasterizer.dir/includes_CXX.rsp -g -std=gnu++17 -o CMakeFiles\\Rasterizer.dir\\main.cpp.obj -c D:\\pet\\CODE\\图形\\HHMM\\Hw3\\main.cpp", "file": "D:/pet/CODE/图形/HHMM/Hw3/main.cpp", "output": "CMakeFiles/Rasterizer.dir/main.cpp.obj"}, {"directory": "D:/pet/CODE/图形/HHMM/Hw3/build", "command": "E:\\mingw64\\bin\\g++.exe  @CMakeFiles/Rasterizer.dir/includes_CXX.rsp -g -std=gnu++17 -o CMakeFiles\\Rasterizer.dir\\rasterizer.cpp.obj -c D:\\pet\\CODE\\图形\\HHMM\\Hw3\\rasterizer.cpp", "file": "D:/pet/CODE/图形/HHMM/Hw3/rasterizer.cpp", "output": "CMakeFiles/Rasterizer.dir/rasterizer.cpp.obj"}, {"directory": "D:/pet/CODE/图形/HHMM/Hw3/build", "command": "E:\\mingw64\\bin\\g++.exe  @CMakeFiles/Rasterizer.dir/includes_CXX.rsp -g -std=gnu++17 -o CMakeFiles\\Rasterizer.dir\\Texture.cpp.obj -c D:\\pet\\CODE\\图形\\HHMM\\Hw3\\Texture.cpp", "file": "D:/pet/CODE/图形/HHMM/Hw3/Texture.cpp", "output": "CMakeFiles/Rasterizer.dir/Texture.cpp.obj"}, {"directory": "D:/pet/CODE/图形/HHMM/Hw3/build", "command": "E:\\mingw64\\bin\\g++.exe  @CMakeFiles/Rasterizer.dir/includes_CXX.rsp -g -std=gnu++17 -o CMakeFiles\\Rasterizer.dir\\Triangle.cpp.obj -c D:\\pet\\CODE\\图形\\HHMM\\Hw3\\Triangle.cpp", "file": "D:/pet/CODE/图形/HHMM/Hw3/Triangle.cpp", "output": "CMakeFiles/Rasterizer.dir/Triangle.cpp.obj"}]