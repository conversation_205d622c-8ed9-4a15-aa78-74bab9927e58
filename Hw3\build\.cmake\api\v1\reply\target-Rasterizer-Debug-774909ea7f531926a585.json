{"artifacts": [{"path": "Rasterizer.exe"}, {"path": "Rasterizer.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 19, "parent": 0}, {"command": 1, "file": 0, "line": 22, "parent": 0}, {"command": 2, "file": 0, "line": 16, "parent": 0}, {"command": 2, "file": 0, "line": 15, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17"}], "includes": [{"backtrace": 3, "path": "E:/eigen-3.4.0"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/mingw_build"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/core/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/flann/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/imgproc/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/ml/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/photo/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/dnn/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/features2d/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/imgcodecs/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/videoio/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/calib3d/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/highgui/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/objdetect/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/stitching/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/ts/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/video/include"}, {"backtrace": 4, "isSystem": true, "path": "E:/opencv/sources/modules/gapi/include"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "17"}, "sourceIndexes": [0, 2, 7, 9]}], "id": "Rasterizer::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"backtrace": 2, "fragment": "E:\\opencv\\mingw_build\\lib\\libopencv_gapi4120.dll.a", "role": "libraries"}, {"backtrace": 2, "fragment": "E:\\opencv\\mingw_build\\lib\\libopencv_highgui4120.dll.a", "role": "libraries"}, {"backtrace": 2, "fragment": "E:\\opencv\\mingw_build\\lib\\libopencv_ml4120.dll.a", "role": "libraries"}, {"backtrace": 2, "fragment": "E:\\opencv\\mingw_build\\lib\\libopencv_objdetect4120.dll.a", "role": "libraries"}, {"backtrace": 2, "fragment": "E:\\opencv\\mingw_build\\lib\\libopencv_photo4120.dll.a", "role": "libraries"}, {"backtrace": 2, "fragment": "E:\\opencv\\mingw_build\\lib\\libopencv_stitching4120.dll.a", "role": "libraries"}, {"backtrace": 2, "fragment": "E:\\opencv\\mingw_build\\lib\\libopencv_video4120.dll.a", "role": "libraries"}, {"backtrace": 2, "fragment": "E:\\opencv\\mingw_build\\lib\\libopencv_videoio4120.dll.a", "role": "libraries"}, {"backtrace": 2, "fragment": "E:\\opencv\\mingw_build\\lib\\libopencv_imgcodecs4120.dll.a", "role": "libraries"}, {"backtrace": 2, "fragment": "E:\\opencv\\mingw_build\\lib\\libopencv_dnn4120.dll.a", "role": "libraries"}, {"backtrace": 2, "fragment": "E:\\opencv\\mingw_build\\lib\\libopencv_calib3d4120.dll.a", "role": "libraries"}, {"backtrace": 2, "fragment": "E:\\opencv\\mingw_build\\lib\\libopencv_features2d4120.dll.a", "role": "libraries"}, {"backtrace": 2, "fragment": "E:\\opencv\\mingw_build\\lib\\libopencv_flann4120.dll.a", "role": "libraries"}, {"backtrace": 2, "fragment": "E:\\opencv\\mingw_build\\lib\\libopencv_imgproc4120.dll.a", "role": "libraries"}, {"backtrace": 2, "fragment": "E:\\opencv\\mingw_build\\lib\\libopencv_core4120.dll.a", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "<PERSON>ster<PERSON>", "nameOnDisk": "Rasterizer.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 7, 9]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 4, 5, 6, 8]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "rasterizer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "rasterizer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "global.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "OBJ_Loader.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Shader.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Texture.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Texture.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Triangle.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Triangle.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}