# 图形学作业编译运行指南

## 项目概述

这是一个基于 C++的图形学光栅化器项目，使用 OpenCV 和 Eigen 库实现三角形渲染。

## 环境要求

### 必需软件

1. **CMake** (版本 3.10 或更高)
2. **Visual Studio** 或 **MinGW** (C++17 支持)
3. **OpenCV** (已配置)
4. **Eigen3** (线性代数库)

### 依赖库路径配置

在 `CMakeLists.txt` 中需要配置以下路径：

```cmake
set(OpenCV_DIR "E:/OpenCV/opencv/build")
set(EIGEN3_INCLUDE_DIR "E:/eigen-3.4.0")
```

**注意**：请根据你的实际安装路径修改这些路径。

## 编译步骤

### 方法一：使用命令行 (推荐)

#### 1. 创建构建目录

```powershell
cd Hw2
mkdir build
cd build
```

#### 2. 生成构建文件

```powershell
cmake ..
```

#### 3. 编译项目

```powershell
cmake --build . --config Release
```

### 方法二：使用 Visual Studio

#### 1. 打开 CMake 项目

- 打开 Visual Studio
- 选择"打开本地文件夹"
- 选择 Hw2 文件夹

#### 2. 配置 CMake 设置

- 在 CMakeSettings.json 中配置编译器
- 确保路径设置正确

#### 3. 构建项目

- 按 Ctrl+Shift+B 构建
- 或使用"生成"菜单

## 运行程序

### 命令行运行

```powershell
# 在build目录下
./Rasterizer.exe
```

### 带参数运行

```powershell
# 输出到指定文件
./Rasterizer.exe output.png
```

## 项目文件结构

```
Hw2/
├── main.cpp              # 主程序入口
├── rasterizer.hpp        # 光栅化器头文件
├── rasterizer.cpp        # 光栅化器实现
├── Triangle.hpp          # 三角形类头文件
├── Triangle.cpp          # 三角形类实现
├── global.hpp            # 全局定义
├── CMakeLists.txt        # CMake构建配置
└── build/                # 构建输出目录
```

## 常见问题解决

### 1. OpenCV 路径错误

**错误**：`Could not find OpenCV`
**解决**：修改 CMakeLists.txt 中的 OpenCV_DIR 路径

### 2. Eigen 路径错误

**错误**：`Could not find Eigen3`
**解决**：修改 CMakeLists.txt 中的 EIGEN3_INCLUDE_DIR 路径

### 3. 编译错误

**错误**：C++17 特性不支持
**解决**：确保使用支持 C++17 的编译器

### 4. 链接错误

**错误**：找不到 OpenCV 库
**解决**：确保 OpenCV 已正确安装并配置环境变量

## 调试技巧

### 1. 检查依赖

```powershell
# 检查CMake是否能找到OpenCV
cmake -L ..
```

### 2. 清理构建

```powershell
# 删除build目录重新构建
rm -rf build
mkdir build
cd build
cmake ..
```

### 3. 详细编译信息

```powershell
# 显示详细编译信息
cmake --build . --config Release --verbose
```

## 输出说明

程序运行后会：

1. 渲染两个三角形
2. 显示在窗口中（交互模式）
3. 或保存为 PNG 文件（命令行模式）

## 扩展开发

### 添加新功能

1. 在相应的.cpp 文件中实现功能
2. 在.hpp 文件中声明接口
3. 重新编译运行

### 修改渲染参数

在 `main.cpp` 中修改：

- 视角参数
- 三角形位置
- 颜色设置

## 注意事项

1. **路径配置**：确保所有依赖库路径正确
2. **编译器版本**：使用支持 C++17 的编译器
3. **权限问题**：确保有写入 build 目录的权限
4. **防火墙**：某些杀毒软件可能阻止程序运行

## 联系支持

如果遇到问题，请检查：

1. 所有依赖是否正确安装
2. 路径配置是否正确
3. 编译器版本是否支持 C++17
